#!/bin/bash

# Icon Conversion Script for Paddy Calculator
# This script converts the SVG icon to required PNG formats

echo "🎨 Converting SVG icon to PNG formats..."

# Check if ImageMagick is installed
if ! command -v convert &> /dev/null; then
    echo "❌ ImageMagick not found. Please install it first:"
    echo "   macOS: brew install imagemagick"
    echo "   Ubuntu: sudo apt-get install imagemagick"
    echo "   Windows: Download from https://imagemagick.org/"
    exit 1
fi

# Source SVG file
SVG_FILE="custom-icon-template.svg"

if [ ! -f "$SVG_FILE" ]; then
    echo "❌ SVG file not found: $SVG_FILE"
    echo "Please make sure the SVG file exists in the current directory."
    exit 1
fi

echo "📱 Converting to app icons..."

# Convert to main app icon (1024x1024)
convert "$SVG_FILE" -resize 1024x1024 -background transparent assets/icon.png
echo "✅ Created assets/icon.png (1024x1024)"

# Convert to adaptive icon (1024x1024)
convert "$SVG_FILE" -resize 1024x1024 -background transparent assets/adaptive-icon.png
echo "✅ Created assets/adaptive-icon.png (1024x1024)"

# Convert to splash icon (1024x1024)
convert "$SVG_FILE" -resize 1024x1024 -background transparent assets/splash-icon.png
echo "✅ Created assets/splash-icon.png (1024x1024)"

# Convert to favicon (48x48)
convert "$SVG_FILE" -resize 48x48 -background transparent assets/favicon.png
echo "✅ Created assets/favicon.png (48x48)"

echo ""
echo "🎉 Icon conversion completed!"
echo ""
echo "📋 Next steps:"
echo "1. Run: npx expo prebuild --clean --platform android"
echo "2. Build new APK: cd android && ./gradlew assembleRelease"
echo "3. Install and test the new APK"
echo ""
echo "📁 Generated files:"
echo "   - assets/icon.png (1024x1024) - Main app icon"
echo "   - assets/adaptive-icon.png (1024x1024) - Android adaptive icon"
echo "   - assets/splash-icon.png (1024x1024) - Splash screen icon"
echo "   - assets/favicon.png (48x48) - Web favicon"
