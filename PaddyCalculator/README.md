# 🌾 Paddy Calculator - React Native App

A modern, professional React Native application for calculating paddy purchase transactions with SQLite database integration. This app is designed for farmers, traders, and agricultural businesses to manage paddy purchase calculations efficiently.

## ✨ Features

### 📊 Core Functionality
- **Dynamic Entry Forms**: Add multiple paddy purchase entries with validation
- **Real-time Calculations**: Automatic calculation of net weight, waste, adjusted weight, and pricing
- **Yield Calculations**: Calculate yield ratios, bags, and remaining quantities
- **Professional Results Display**: Comprehensive results with totals and summaries

### 💾 Data Management
- **SQLite Database**: Local data storage for offline functionality
- **Session Management**: Group calculations into sessions for better organization
- **History Tracking**: View and search previous calculations and sessions
- **Data Persistence**: Save and retrieve calculation data

### 📄 PDF Generation
- **Professional PDF Reports**: Generate formatted PDF receipts
- **Company Branding**: Customizable company information in reports
- **Detailed Breakdowns**: Complete calculation details in PDF format
- **Share Functionality**: Share PDFs via email, messaging, or cloud storage

### ⚙️ Configuration
- **Settings Management**: Customize default values and company information
- **Flexible Configuration**: Adjust rewash percentages and other parameters
- **User Preferences**: Personalize the app experience

## 🏗️ Technical Architecture

### Frontend
- **React Native**: Cross-platform mobile development
- **Expo**: Development platform and build tools
- **React Navigation**: Screen navigation and routing
- **React Native Paper**: Material Design UI components

### Backend & Storage
- **Expo SQLite**: Local database for data persistence
- **Async Storage**: Settings and preferences storage

### PDF & Sharing
- **Expo Print**: PDF generation functionality
- **Expo Sharing**: Share PDFs across platforms
- **Expo File System**: File management

## 📱 Screens

### 1. Calculator Screen
- Dynamic entry forms with validation
- Real-time calculation results
- Action buttons for calculate, save, and PDF generation
- Floating action button to add new entries

### 2. History Screen
- View all calculation sessions
- Search and filter functionality
- Detailed session information
- Individual entry management

### 3. Settings Screen
- Company information configuration
- Default calculation parameters
- App information and credits

## 🧮 Calculation Logic

The app implements the same calculation logic as the original Python version:

```javascript
// Core calculations
netWeight = grossWeight - tractorWeight
wasteWeight = Math.ceil((netWeight / 1000) * rewashPercentage)
adjustedWeight = netWeight - wasteWeight
calculatedPrice = (adjustedWeight / 900) * pricePer900

// Yield calculations
yieldRatio = Math.floor(adjustedWeight / 900)
paddyWeight = adjustedWeight - (yieldRatio * 900)
numBags = Math.floor(paddyWeight / 45)
remainingKg = paddyWeight % 45
```

## 🚀 Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn
- Expo CLI (`npm install -g @expo/cli`)
- EAS CLI (`npm install -g eas-cli`)
- Android Studio (for Android development)
- Xcode (for iOS development, macOS only)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd PaddyCalculator
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm start
   ```

4. **Run on device/simulator**
   - **Web**: Press `w` in the terminal
   - **Android**: Press `a` in the terminal (requires Android Studio)
   - **iOS**: Press `i` in the terminal (requires Xcode, macOS only)
   - **Physical Device**: Scan QR code with Expo Go app

## 📁 Project Structure

```
PaddyCalculator/
├── src/
│   ├── components/          # Reusable UI components
│   │   ├── EntryForm.js     # Dynamic entry form component
│   │   └── ResultsDisplay.js # Results display component
│   ├── screens/             # App screens
│   │   ├── CalculatorScreen.js # Main calculator screen
│   │   ├── HistoryScreen.js    # History and sessions screen
│   │   └── SettingsScreen.js   # Settings and configuration
│   ├── database/            # Database operations
│   │   └── database.js      # SQLite database setup and operations
│   ├── utils/               # Utility functions
│   │   ├── calculations.js  # Core calculation logic
│   │   └── pdfGenerator.js  # PDF generation utilities
│   └── constants/           # App constants
│       └── theme.js         # Theme and styling constants
├── App.js                   # Main app component
├── package.json             # Dependencies and scripts
└── README.md               # This file
```

## 🎨 Design System

The app uses a modern, flexible design system with:

- **Color Palette**: Green primary colors reflecting agricultural theme
- **Typography**: Clear, readable fonts with proper hierarchy
- **Spacing**: Consistent spacing system for better UX
- **Components**: Material Design components via React Native Paper
- **Responsive**: Adapts to different screen sizes

## 📱 Building for Production

### Using EAS Build (Recommended)

1. **Configure EAS**:
```bash
eas login
eas build:configure
```

2. **Build for Android**:
```bash
# Development build
eas build --platform android --profile development

# Production build
eas build --platform android --profile production
```

3. **Build for iOS**:
```bash
# Development build
eas build --platform ios --profile development

# Production build
eas build --platform ios --profile production
```

4. **Submit to App Stores**:
```bash
# Submit to Google Play Store
eas submit --platform android

# Submit to Apple App Store
eas submit --platform ios
```

### Local Builds

1. **Android APK**:
```bash
npx expo build:android
```

2. **iOS IPA**:
```bash
npx expo build:ios
```

## 🔧 Configuration

### Default Settings
- **Rewash Percentage**: 2.0%
- **Company Name**: CHEVURU SOFTWARE SOLUTIONS
- **Company Address**: West Street, Kagithalapuru, Pincode 524405
- **Phone**: +91 **********
- **Email**: <EMAIL>
- **Website**: www.chevurusoftwaresolutions.com

All settings can be customized through the Settings screen.

## 📊 Database Schema

### Tables
1. **paddy_entries**: Individual calculation entries
2. **calculation_sessions**: Grouped calculation sessions
3. **session_entries**: Junction table for session-entry relationships
4. **app_settings**: Application configuration settings

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 👨‍💻 Developer

**CHEVURU SOFTWARE SOLUTIONS**
- 📧 Email: <EMAIL>
- 🌐 Website: www.chevurusoftwaresolutions.com
- 📱 Phone: +91 **********

## 🙏 Acknowledgments

- Built with React Native and Expo
- UI components from React Native Paper
- Inspired by the original Python/Kivy implementation
- Designed for the agricultural community

---

**🌾 Paddy Calculator - Professional paddy purchase management made simple!**
