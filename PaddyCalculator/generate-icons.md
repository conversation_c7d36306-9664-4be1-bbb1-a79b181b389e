# 🎨 Icon Generation Guide for Paddy Calculator

## 📋 Icon Requirements

### Main App Icon (icon.png)
- **Size**: 1024x1024 pixels
- **Format**: PNG
- **Usage**: iOS app icon, general app representation
- **Design**: Should be recognizable at small sizes

### Android Adaptive Icon (adaptive-icon.png)
- **Size**: 1024x1024 pixels
- **Format**: PNG
- **Usage**: Android adaptive icon foreground
- **Design**: Should work well with different background shapes
- **Safe Area**: Keep important elements within 768x768 center area

### Favicon (favicon.png)
- **Size**: 48x48 pixels (or generate from main icon)
- **Format**: PNG
- **Usage**: Web browser tab icon

### Splash Icon (splash-icon.png)
- **Size**: 1024x1024 pixels
- **Format**: PNG
- **Usage**: App loading screen

## 🎨 Design Suggestions for Paddy Calculator

### Theme Ideas:
1. **Rice/Paddy Grains**: Stylized rice grains with calculator elements
2. **Tractor + Calculator**: Combine farming equipment with calculation symbols
3. **Scales + Rice**: Balance scales with rice grains
4. **Modern Farming**: Clean, modern representation of agriculture + technology

### Color Scheme:
- **Primary**: Dark Green (#2D5016 or similar)
- **Secondary**: Golden Yellow (#FFD700 or similar)
- **Background**: White or light green

## 🛠️ Tools for Icon Creation

### Online Tools:
1. **Canva**: Easy drag-and-drop icon creator
2. **Figma**: Professional design tool (free)
3. **Adobe Express**: Quick icon generator
4. **Icon8**: Icon generator and editor

### Professional Tools:
1. **Adobe Illustrator**: Vector-based design
2. **Sketch**: Mac-only design tool
3. **Affinity Designer**: Affordable alternative

## 📱 Icon Generation Services

### Automated Generation:
1. **App Icon Generator**: Upload 1024x1024, get all sizes
2. **Expo Icon Generator**: Built into Expo CLI
3. **Icon Kitchen**: Android adaptive icon generator

## 🔄 After Creating Your Icon

1. Replace the files in `/assets/` folder
2. Run: `npx expo prebuild --clean` (regenerates native projects)
3. Build new APK: `cd android && ./gradlew assembleRelease`

## ✅ Verification

After adding your icon:
- Check app drawer on Android
- Verify icon appears correctly in different sizes
- Test adaptive icon on Android (different launcher shapes)
- Confirm splash screen shows your icon
