import React, { useEffect, useState } from 'react';
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { Provider as PaperProvider } from 'react-native-paper';
import { View, StyleSheet } from 'react-native';
import { Text } from 'react-native-paper';
import { initDatabase } from './src/database/database';
import CalculatorScreen from './src/screens/CalculatorScreen';
import HistoryScreen from './src/screens/HistoryScreen';
import SavedBillsScreen from './src/screens/SavedBillsScreen';
import SettingsScreen from './src/screens/SettingsScreen';
import FarmerManagementScreen from './src/screens/FarmerManagementScreen';
import TractorManagementScreen from './src/screens/TractorManagementScreen';
import BillDetailsScreen from './src/screens/BillDetailsScreen';
import AddFarmerScreen from './src/screens/AddFarmerScreen';
import AddTractorScreen from './src/screens/AddTractorScreen';

const Stack = createStackNavigator();

export default function App() {
  const [isDbInitialized, setIsDbInitialized] = useState(false);
  const [dbError, setDbError] = useState(null);

  useEffect(() => {
    initializeDatabase();
  }, []);

  const initializeDatabase = async () => {
    try {
      await initDatabase();
      setIsDbInitialized(true);
      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Failed to initialize database:', error);
      setDbError(error.message);
    }
  };

  if (dbError) {
    return (
      <PaperProvider>
        <View style={styles.errorContainer}>
          <Text style={styles.errorTitle}>Database Error</Text>
          <Text style={styles.errorText}>{dbError}</Text>
        </View>
      </PaperProvider>
    );
  }

  if (!isDbInitialized) {
    return (
      <PaperProvider>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Initializing Paddy Calculator...</Text>
        </View>
      </PaperProvider>
    );
  }

  return (
    <PaperProvider>
      <NavigationContainer>
        <Stack.Navigator
          initialRouteName="Calculator"
          screenOptions={{
            headerShown: false,
          }}
        >
          <Stack.Screen
            name="Calculator"
            component={CalculatorScreen}
            options={{ title: 'Paddy Calculator' }}
          />
          <Stack.Screen
            name="History"
            component={HistoryScreen}
            options={{ title: 'Calculation History' }}
          />
          <Stack.Screen
            name="SavedBills"
            component={SavedBillsScreen}
            options={{ title: 'Saved Bills' }}
          />
          <Stack.Screen
            name="Settings"
            component={SettingsScreen}
            options={{ title: 'Settings' }}
          />
          <Stack.Screen
            name="FarmerManagement"
            component={FarmerManagementScreen}
            options={{ title: 'Farmer Management' }}
          />
          <Stack.Screen
            name="TractorManagement"
            component={TractorManagementScreen}
            options={{ title: 'Tractor Management' }}
          />
          <Stack.Screen
            name="BillDetails"
            component={BillDetailsScreen}
            options={{ title: 'Bill Details' }}
          />
          <Stack.Screen
            name="AddFarmer"
            component={AddFarmerScreen}
            options={{ title: 'Add Farmer' }}
          />
          <Stack.Screen
            name="AddTractor"
            component={AddTractorScreen}
            options={{ title: 'Add Tractor' }}
          />
        </Stack.Navigator>
      </NavigationContainer>
      <StatusBar style="auto" />
    </PaperProvider>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    fontSize: 18,
    color: '#2E7D32',
    fontWeight: 'bold',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  errorTitle: {
    fontSize: 20,
    color: '#F44336',
    fontWeight: 'bold',
    marginBottom: 10,
  },
  errorText: {
    fontSize: 16,
    color: '#757575',
    textAlign: 'center',
  },

});
