<?xml version="1.0" encoding="UTF-8"?>
<svg width="1024" height="1024" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Circle -->
  <circle cx="512" cy="512" r="480" fill="#2D5016" stroke="#FFD700" stroke-width="20"/>
  
  <!-- Rice Grains Background Pattern -->
  <g opacity="0.1">
    <ellipse cx="200" cy="200" rx="8" ry="20" fill="#FFD700" transform="rotate(45 200 200)"/>
    <ellipse cx="300" cy="150" rx="8" ry="20" fill="#FFD700" transform="rotate(30 300 150)"/>
    <ellipse cx="400" cy="180" rx="8" ry="20" fill="#FFD700" transform="rotate(60 400 180)"/>
    <ellipse cx="150" cy="300" rx="8" ry="20" fill="#FFD700" transform="rotate(15 150 300)"/>
    <ellipse cx="800" cy="200" rx="8" ry="20" fill="#FFD700" transform="rotate(45 800 200)"/>
    <ellipse cx="850" cy="300" rx="8" ry="20" fill="#FFD700" transform="rotate(30 850 300)"/>
    <ellipse cx="750" cy="150" rx="8" ry="20" fill="#FFD700" transform="rotate(60 750 150)"/>
  </g>
  
  <!-- Calculator Body -->
  <rect x="312" y="200" width="400" height="500" rx="40" fill="#FFFFFF" stroke="#2D5016" stroke-width="8"/>
  
  <!-- Calculator Screen -->
  <rect x="352" y="240" width="320" height="80" rx="10" fill="#1a1a1a"/>
  
  <!-- Calculator Display Text -->
  <text x="512" y="290" text-anchor="middle" fill="#00FF00" font-family="monospace" font-size="36" font-weight="bold">1234.56</text>
  
  <!-- Calculator Buttons Grid -->
  <!-- Row 1 -->
  <rect x="372" y="350" width="60" height="50" rx="8" fill="#FFD700" stroke="#2D5016" stroke-width="2"/>
  <text x="402" y="380" text-anchor="middle" fill="#2D5016" font-family="Arial" font-size="20" font-weight="bold">C</text>
  
  <rect x="452" y="350" width="60" height="50" rx="8" fill="#FFD700" stroke="#2D5016" stroke-width="2"/>
  <text x="482" y="380" text-anchor="middle" fill="#2D5016" font-family="Arial" font-size="20" font-weight="bold">÷</text>
  
  <rect x="532" y="350" width="60" height="50" rx="8" fill="#FFD700" stroke="#2D5016" stroke-width="2"/>
  <text x="562" y="380" text-anchor="middle" fill="#2D5016" font-family="Arial" font-size="20" font-weight="bold">×</text>
  
  <rect x="612" y="350" width="60" height="50" rx="8" fill="#FFD700" stroke="#2D5016" stroke-width="2"/>
  <text x="642" y="380" text-anchor="middle" fill="#2D5016" font-family="Arial" font-size="20" font-weight="bold">-</text>
  
  <!-- Row 2 -->
  <rect x="372" y="420" width="60" height="50" rx="8" fill="#F0F0F0" stroke="#2D5016" stroke-width="2"/>
  <text x="402" y="450" text-anchor="middle" fill="#2D5016" font-family="Arial" font-size="20" font-weight="bold">7</text>
  
  <rect x="452" y="420" width="60" height="50" rx="8" fill="#F0F0F0" stroke="#2D5016" stroke-width="2"/>
  <text x="482" y="450" text-anchor="middle" fill="#2D5016" font-family="Arial" font-size="20" font-weight="bold">8</text>
  
  <rect x="532" y="420" width="60" height="50" rx="8" fill="#F0F0F0" stroke="#2D5016" stroke-width="2"/>
  <text x="562" y="450" text-anchor="middle" fill="#2D5016" font-family="Arial" font-size="20" font-weight="bold">9</text>
  
  <rect x="612" y="420" width="60" height="120" rx="8" fill="#FFD700" stroke="#2D5016" stroke-width="2"/>
  <text x="642" y="485" text-anchor="middle" fill="#2D5016" font-family="Arial" font-size="20" font-weight="bold">+</text>
  
  <!-- Row 3 -->
  <rect x="372" y="490" width="60" height="50" rx="8" fill="#F0F0F0" stroke="#2D5016" stroke-width="2"/>
  <text x="402" y="520" text-anchor="middle" fill="#2D5016" font-family="Arial" font-size="20" font-weight="bold">4</text>
  
  <rect x="452" y="490" width="60" height="50" rx="8" fill="#F0F0F0" stroke="#2D5016" stroke-width="2"/>
  <text x="482" y="520" text-anchor="middle" fill="#2D5016" font-family="Arial" font-size="20" font-weight="bold">5</text>
  
  <rect x="532" y="490" width="60" height="50" rx="8" fill="#F0F0F0" stroke="#2D5016" stroke-width="2"/>
  <text x="562" y="520" text-anchor="middle" fill="#2D5016" font-family="Arial" font-size="20" font-weight="bold">6</text>
  
  <!-- Row 4 -->
  <rect x="372" y="560" width="60" height="50" rx="8" fill="#F0F0F0" stroke="#2D5016" stroke-width="2"/>
  <text x="402" y="590" text-anchor="middle" fill="#2D5016" font-family="Arial" font-size="20" font-weight="bold">1</text>
  
  <rect x="452" y="560" width="60" height="50" rx="8" fill="#F0F0F0" stroke="#2D5016" stroke-width="2"/>
  <text x="482" y="590" text-anchor="middle" fill="#2D5016" font-family="Arial" font-size="20" font-weight="bold">2</text>
  
  <rect x="532" y="560" width="60" height="50" rx="8" fill="#F0F0F0" stroke="#2D5016" stroke-width="2"/>
  <text x="562" y="590" text-anchor="middle" fill="#2D5016" font-family="Arial" font-size="20" font-weight="bold">3</text>
  
  <rect x="612" y="560" width="60" height="120" rx="8" fill="#4CAF50" stroke="#2D5016" stroke-width="2"/>
  <text x="642" y="625" text-anchor="middle" fill="#FFFFFF" font-family="Arial" font-size="18" font-weight="bold">=</text>
  
  <!-- Row 5 -->
  <rect x="372" y="630" width="140" height="50" rx="8" fill="#F0F0F0" stroke="#2D5016" stroke-width="2"/>
  <text x="442" y="660" text-anchor="middle" fill="#2D5016" font-family="Arial" font-size="20" font-weight="bold">0</text>
  
  <rect x="532" y="630" width="60" height="50" rx="8" fill="#F0F0F0" stroke="#2D5016" stroke-width="2"/>
  <text x="562" y="660" text-anchor="middle" fill="#2D5016" font-family="Arial" font-size="20" font-weight="bold">.</text>
  
  <!-- Rice Grains Decoration -->
  <g transform="translate(200, 750)">
    <ellipse cx="0" cy="0" rx="12" ry="30" fill="#FFD700" transform="rotate(15)"/>
    <ellipse cx="25" cy="5" rx="12" ry="30" fill="#FFD700" transform="rotate(45)"/>
    <ellipse cx="50" cy="-5" rx="12" ry="30" fill="#FFD700" transform="rotate(75)"/>
    <ellipse cx="75" cy="10" rx="12" ry="30" fill="#FFD700" transform="rotate(105)"/>
  </g>
  
  <g transform="translate(750, 750)">
    <ellipse cx="0" cy="0" rx="12" ry="30" fill="#FFD700" transform="rotate(-15)"/>
    <ellipse cx="-25" cy="5" rx="12" ry="30" fill="#FFD700" transform="rotate(-45)"/>
    <ellipse cx="-50" cy="-5" rx="12" ry="30" fill="#FFD700" transform="rotate(-75)"/>
    <ellipse cx="-75" cy="10" rx="12" ry="30" fill="#FFD700" transform="rotate(-105)"/>
  </g>
  
  <!-- App Title -->
  <text x="512" y="850" text-anchor="middle" fill="#FFD700" font-family="Arial" font-size="48" font-weight="bold">PADDY</text>
  <text x="512" y="900" text-anchor="middle" fill="#FFD700" font-family="Arial" font-size="32" font-weight="normal">Calculator</text>
</svg>
