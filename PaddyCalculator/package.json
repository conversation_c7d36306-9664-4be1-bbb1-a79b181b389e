{"name": "paddy-calculator", "version": "1.0.0", "description": "Professional paddy purchase management system for farmers and traders", "main": "index.js", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "keywords": ["paddy", "calculator", "farming", "agriculture", "business", "react-native", "expo"], "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "build:android": "expo build:android", "build:ios": "expo build:ios", "build:web": "expo build:web", "eject": "expo eject", "test": "jest"}, "dependencies": {"@expo/config-plugins": "~10.1.1", "@expo/prebuild-config": "~9.0.0", "@react-native-community/datetimepicker": "8.4.1", "@react-navigation/bottom-tabs": "^7.4.4", "@react-navigation/native": "^7.1.16", "@react-navigation/stack": "^7.4.4", "eas-cli": "^16.17.3", "expo": "~53.0.20", "expo-file-system": "^18.1.11", "expo-linear-gradient": "^14.1.5", "expo-print": "^14.1.4", "expo-sharing": "^13.1.5", "expo-sqlite": "^15.2.14", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.5", "react-native-paper": "^5.14.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-uuid": "^2.0.3", "react-native-vector-icons": "^10.3.0"}, "devDependencies": {"@babel/core": "^7.20.0"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"listUnknownPackages": false}}}, "private": true}