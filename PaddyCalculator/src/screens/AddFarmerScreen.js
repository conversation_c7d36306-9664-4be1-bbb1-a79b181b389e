import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { 
  IconButton,
  Text,
  Snackbar
} from 'react-native-paper';
import { Colors, Spacing, Typography, BorderRadius } from '../constants/theme';
import { insertFarmer } from '../database/database';
import FarmerForm from '../components/FarmerForm';

const AddFarmerScreen = ({ navigation }) => {
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  const handleFormSubmit = async (formData) => {
    try {
      await insertFarmer(formData);
      showSnackbar('Farmer added successfully');
      // Navigate back after a short delay
      setTimeout(() => {
        navigation.goBack();
      }, 1500);
    } catch (error) {
      
      showSnackbar('Error saving farmer');
    }
  };

  const handleFormCancel = () => {
    navigation.goBack();
  };

  const showSnackbar = (message) => {
    setSnackbarMessage(message);
    setSnackbarVisible(true);
  };

  return (
    <View style={styles.container}>
      {/* Modern Header */}
      <View style={styles.headerContainer}>
        <View style={styles.headerContent}>
          <View style={styles.headerTop}>
            <IconButton
              icon="arrow-left"
              size={24}
              iconColor={Colors.textLight}
              onPress={() => navigation.goBack()}
              style={styles.backButton}
            />
            <View style={styles.headerTitleContainer}>
              <View style={styles.headerTitleRow}>
                <IconButton
                  icon="account-plus"
                  size={24}
                  iconColor={Colors.secondary}
                  style={styles.headerIcon}
                />
                <Text style={styles.headerTitle}>Add New Farmer</Text>
              </View>
              <Text style={styles.headerSubtitle}>Create a new farmer profile</Text>
            </View>
            <IconButton
              icon="refresh"
              size={24}
              iconColor={Colors.textLight}
              onPress={() => navigation.replace('AddFarmer')}
              style={styles.refreshButton}
            />
          </View>
        </View>
      </View>

      {/* Form Content */}
      <FarmerForm
        title="Add New Farmer"
        onSubmit={handleFormSubmit}
        onCancel={handleFormCancel}
      />

      {/* Snackbar */}
      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
        style={styles.snackbar}
      >
        {snackbarMessage}
      </Snackbar>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  // Modern Header Styles
  headerContainer: {
    backgroundColor: Colors.primary,
    paddingTop: 50,
    paddingBottom: Spacing.lg,
    borderBottomLeftRadius: BorderRadius.xl,
    borderBottomRightRadius: BorderRadius.xl,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  headerContent: {
    paddingHorizontal: Spacing.lg,
  },
  headerTop: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    margin: 0,
  },
  refreshButton: {
    margin: 0,
  },
  headerTitleContainer: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerIcon: {
    margin: 0,
    marginRight: 8,
  },
  headerTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.textLight,
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: Typography.fontSize.sm,
    color: Colors.textLight,
    opacity: 0.9,
    textAlign: 'center',
    marginTop: 2,
  },
  snackbar: {
    backgroundColor: Colors.primary,
    marginBottom: 20, // Position above any potential bottom elements
    elevation: 10, // Higher elevation
  },
});

export default AddFarmerScreen;
