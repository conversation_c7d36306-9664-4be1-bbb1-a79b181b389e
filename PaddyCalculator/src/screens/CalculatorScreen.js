import React, { useState, useEffect, useCallback } from 'react';
import { View, StyleSheet, ScrollView, Alert, StatusBar, Animated } from 'react-native';
import { Appbar, FAB, Button, Snackbar, Card, Text, Surface, IconButton } from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import { useFocusEffect } from '@react-navigation/native';
import TractorWithTrolley from '../components/TractorWithTrolley';
import ModernConfirmDialog from '../components/ModernConfirmDialog';
import EntryForm from '../components/EntryForm';
import ResultsDisplay from '../components/ResultsDisplay';
import { Colors, Spacing, Layout, Typography, BorderRadius } from '../constants/theme';
import { 
  calculatePaddyEntry, 
  calculateTotals, 
  validateEntryInput,
  calculateBatchSummary 
} from '../utils/calculations';
import {
  insertPaddyEntry,
  createCalculationSession,
  getAllFarmers,
  getAllTractors,
  getSetting
} from '../database/database';
import { generateAndSharePDF } from '../utils/pdfGenerator';

const CalculatorScreen = ({ navigation }) => {
  const [entries, setEntries] = useState([]);
  const [calculatedEntries, setCalculatedEntries] = useState([]);
  const [totals, setTotals] = useState(null);
  const [serialNumber, setSerialNumber] = useState(1);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [expandedEntryId, setExpandedEntryId] = useState(null);
  const [clearAllDialogVisible, setClearAllDialogVisible] = useState(false);
  const [globalBuyerId, setGlobalBuyerId] = useState(null);
  const [globalSellerId, setGlobalSellerId] = useState(null);
  const [globalPricePer900, setGlobalPricePer900] = useState('');
  const [globalRewashPercentage, setGlobalRewashPercentage] = useState('2.0');

  // Tractor animation
  const tractorAnimation = useState(new Animated.Value(0))[0];

  // Load default rewash percentage from settings
  const loadDefaultRewashPercentage = async () => {
    try {
      const defaultRewash = await getSetting('default_rewash_percentage');
      if (defaultRewash) {
        setGlobalRewashPercentage(defaultRewash);
      }
    } catch (error) {
      
    }
  };

  // Format currency for compact display
  const formatCurrency = (amount) => {
    if (!amount) return '₹0';

    const num = Math.round(amount);

    if (num >= 10000000) { // 1 crore
      return `₹${(num / 10000000).toFixed(1)}Cr`;
    } else if (num >= 100000) { // 1 lakh
      return `₹${(num / 100000).toFixed(1)}L`;
    } else if (num >= 1000) { // 1 thousand
      return `₹${(num / 1000).toFixed(1)}K`;
    } else {
      return `₹${num}`;
    }
  };

  // Initialize with one empty form (collapsed by default)
  useEffect(() => {
    addNewEntry(false);
  }, []);

  // Start tractor animation
  useEffect(() => {
    const startTractorAnimation = () => {
      Animated.loop(
        Animated.timing(tractorAnimation, {
          toValue: 1,
          duration: 8000, // 8 seconds for full cycle
          useNativeDriver: true,
        })
      ).start();
    };

    startTractorAnimation();
  }, []);

  // Refresh dropdown data and load settings when returning to this screen
  useFocusEffect(
    useCallback(() => {
      setRefreshTrigger(prev => prev + 1);
      loadDefaultRewashPercentage();
    }, [])
  );

  // Recalculate totals when calculated entries change
  useEffect(() => {
    if (calculatedEntries.length > 0) {
      const newTotals = calculateTotals(calculatedEntries);
      setTotals(newTotals);
    } else {
      setTotals(null);
    }
  }, [calculatedEntries]);

  const addNewEntry = (autoExpand = true) => {
    const newEntry = {
      id: Date.now(),
      serialNumber: serialNumber,
      data: {}
    };
    setEntries(prev => [...prev, newEntry]);
    setSerialNumber(prev => prev + 1);
    // Auto-expand the new entry and collapse others (only if autoExpand is true)
    if (autoExpand) {
      setExpandedEntryId(newEntry.id);
    }
  };

  const removeEntry = (entryId) => {
    

    // Filter out the removed entry and update serial numbers in one operation
    setEntries(prev => {
      const filteredEntries = prev.filter(entry => entry.id !== entryId);
      // Renumber all remaining entries
      const renumberedEntries = filteredEntries.map((entry, index) => ({
        ...entry,
        serialNumber: index + 1
      }));

      // Update the serial number counter for next new entry
      setSerialNumber(renumberedEntries.length + 1);

      return renumberedEntries;
    });

    // Also remove from calculated entries if it exists
    setCalculatedEntries(prev => prev.filter(entry => entry.id !== entryId));

    // If the removed entry was expanded, clear the expanded state
    if (expandedEntryId === entryId) {
      setExpandedEntryId(null);
    }

    
  };

  const toggleEntryExpansion = (entryId) => {
    setExpandedEntryId(prev => prev === entryId ? null : entryId);
  };

  const handleEntrySubmit = (entryId, formData) => {
    try {
      // Validate input
      const validation = validateEntryInput(formData);
      if (!validation.isValid) {
        const errorMessages = Object.values(validation.errors).join('\n');
        showSnackbar(`Validation Error:\n${errorMessages}`);
        return;
      }

      // Calculate entry
      const calculatedEntry = calculatePaddyEntry(formData);
      calculatedEntry.id = entryId;

      // Update calculated entries
      setCalculatedEntries(prev => {
        const existingIndex = prev.findIndex(entry => entry.id === entryId);
        if (existingIndex >= 0) {
          // Update existing entry
          const updated = [...prev];
          updated[existingIndex] = calculatedEntry;
          return updated;
        } else {
          // Add new entry
          return [...prev, calculatedEntry];
        }
      });

      showSnackbar('Entry calculated successfully!');
    } catch (error) {
      showSnackbar(`Calculation Error: ${error.message}`);
    }
  };

  const calculateAll = () => {
    if (entries.length === 0) {
      showSnackbar('No entries to calculate');
      return;
    }

    let successCount = 0;
    const newCalculatedEntries = [];

    entries.forEach((entry, index) => {
      try {
        if (entry.data && Object.keys(entry.data).length > 0) {
          const validation = validateEntryInput(entry.data);

          if (validation.isValid) {
            const calculatedEntry = calculatePaddyEntry(entry.data);
            calculatedEntry.id = entry.id;
            newCalculatedEntries.push(calculatedEntry);
            successCount++;
          }
        }
      } catch (error) {
        // Silent error handling for production
      }
    });

    setCalculatedEntries(newCalculatedEntries);

    if (successCount > 0) {
      showSnackbar(`${successCount} entries calculated successfully!`);
    } else {
      showSnackbar('No valid entries found to calculate');
    }
  };

  const saveSession = async () => {
    if (calculatedEntries.length === 0) {
      showSnackbar('No calculated entries to save');
      return;
    }

    try {
      // Generate a unique bill number for this session
      const timestamp = new Date().toISOString().replace(/[-:T.]/g, '').slice(0, 14);
      const randomSuffix = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
      const generatedBillNo = `B${timestamp}${randomSuffix}`;

      // Save individual entries to database with the generated bill number
      const entryIds = [];
      for (const entry of calculatedEntries) {
        // Add the generated bill number to each entry
        const entryWithBillNo = {
          ...entry,
          generatedBillNo: generatedBillNo
        };
        const entryId = await insertPaddyEntry(entryWithBillNo);
        entryIds.push(entryId);
      }

      // Create session
      const batchSummary = calculateBatchSummary(calculatedEntries);
      const sessionId = await createCalculationSession(
        generatedBillNo, // Use generated bill number as session name
        entryIds,
        batchSummary.totalPrice
      );

      showSnackbar(`Session saved successfully! Bill No: ${generatedBillNo}`);
    } catch (error) {
      
      showSnackbar(`Error saving session: ${error.message}`);
    }
  };

  const generatePDF = async () => {
    if (calculatedEntries.length === 0) {
      showSnackbar('No calculated entries to generate PDF');
      return;
    }

    try {
      showSnackbar('Generating PDF...');

      // Load farmer and tractor data for PDF
      const [farmers, tractors] = await Promise.all([
        getAllFarmers(),
        getAllTractors()
      ]);

      await generateAndSharePDF(calculatedEntries, {}, farmers, tractors);
      showSnackbar('PDF generated and shared successfully!');
    } catch (error) {
      console.error('PDF Generation Error:', error);
      console.error('Error stack:', error.stack);
      showSnackbar(`Error generating PDF: ${error.message}`);
    }
  };

  const clearAll = () => {
    setClearAllDialogVisible(true);
  };

  const handleClearAllConfirm = () => {
    // Clear all state and reset serial number
    setEntries([]);
    setCalculatedEntries([]);
    setTotals(null);
    setSerialNumber(2); // Set to 2 because we'll add entry with serialNumber 1
    setGlobalBuyerId(null);
    setGlobalSellerId(null);
    setGlobalPricePer900('');
    setGlobalRewashPercentage('2.0');

    // Add one empty entry with serialNumber 1
    setTimeout(() => {
      const newEntry = {
        id: Date.now(),
        serialNumber: 1,
        data: {}
      };
      setEntries([newEntry]);
    }, 100);

    showSnackbar('All entries cleared successfully!');
  };

  const showSnackbar = (message) => {
    setSnackbarMessage(message);
    setSnackbarVisible(true);
  };

  const handleGlobalFieldChange = (field, value) => {
    

    // Update global state based on field
    if (field === 'buyerId') {
      setGlobalBuyerId(value);
    } else if (field === 'sellerId') {
      setGlobalSellerId(value);
    } else if (field === 'pricePer900') {
      setGlobalPricePer900(value);
    } else if (field === 'rewashPercentage') {
      setGlobalRewashPercentage(value);
    }

    // Update all entries with the new global value
    setEntries(prev => prev.map(entry => ({
      ...entry,
      data: {
        ...entry.data,
        [field]: value
      }
    })));
  };

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={Colors.primary} barStyle="light-content" />

      {/* Modern Header */}
      <View style={styles.modernHeader}>
        <LinearGradient
          colors={[Colors.primary, Colors.primaryLight, Colors.secondary]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.gradientHeader}
        >
          <View style={styles.headerContent}>
            <View style={styles.headerLeft}>
              <View style={styles.appIcon}>
                <IconButton
                  icon="calculator-variant"
                  size={28}
                  iconColor={Colors.textLight}
                  style={styles.appIconButton}
                />
              </View>
              <View style={styles.headerTextContainer}>
                <Text style={styles.appTitle}>Paddy Calculator</Text>
                <Text style={styles.appSubtitle}>Rice Trading Platform</Text>
              </View>
            </View>
            <View style={styles.headerRight}>
              <View style={styles.paddyIcon}>
                <IconButton
                  icon="grass"
                  size={32}
                  iconColor={Colors.textLight}
                  style={styles.paddyIconButton}
                />
              </View>
            </View>
          </View>
        </LinearGradient>

        {/* Stats Dashboard */}
        <View style={styles.statsRow}>
          <View style={styles.modernStatCard}>
            <Text style={styles.statValue}>{entries.length}</Text>
            <Text style={styles.statLabel}>Entries</Text>
          </View>
          <View style={styles.modernStatCard}>
            <Text style={styles.statValue}>{calculatedEntries.length}</Text>
            <Text style={styles.statLabel}>Calculated</Text>
          </View>
          <View style={styles.modernStatCard}>
            <Text style={styles.statValue} numberOfLines={1} adjustsFontSizeToFit>
              {totals && totals.totalPrice ? formatCurrency(totals.totalPrice) : '₹0'}
            </Text>
            <Text style={styles.statLabel}>Total Value</Text>
          </View>
        </View>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Entry Forms */}
        <View style={styles.formsContainer}>
          {entries.map((entry, index) => (
            <EntryForm
              key={entry.id}
              serialNumber={entry.serialNumber}
              initialData={entry.data}
              refreshTrigger={refreshTrigger}
              isFirstEntry={index === 0}
              globalBuyerId={globalBuyerId}
              globalSellerId={globalSellerId}
              globalPricePer900={globalPricePer900}
              globalRewashPercentage={globalRewashPercentage}
              onGlobalFieldChange={handleGlobalFieldChange}
              onSubmit={(formData) => {
                // Update entry data only - calculation happens via "Calculate All"
                setEntries(prev => prev.map(e =>
                  e.id === entry.id ? { ...e, data: formData } : e
                ));
              }}
              onRemove={() => removeEntry(entry.id)}
              showRemoveButton={entries.length > 1}
              isExpanded={expandedEntryId === entry.id}
              onExpandToggle={() => toggleEntryExpansion(entry.id)}
            />
          ))}
        </View>

        {/* Modern Action Buttons */}
        <View style={styles.modernActionSection}>
          <View style={styles.primaryActions}>
            <Button
              mode="contained"
              icon="calculator"
              onPress={calculateAll}
              style={styles.modernPrimaryButton}
              contentStyle={styles.modernButtonContent}
              labelStyle={styles.modernButtonLabel}
              buttonColor={Colors.primary}
            >
              Calculate All
            </Button>
          </View>

          <View style={styles.secondaryActions}>
            <Button
              mode="contained"
              icon="content-save"
              onPress={saveSession}
              style={styles.modernSecondaryButton}
              contentStyle={styles.modernSecondaryButtonContent}
              labelStyle={styles.modernSecondaryButtonLabel}
              disabled={calculatedEntries.length === 0}
              buttonColor={Colors.primary}
            >
              Save
            </Button>

            <Button
              mode="contained"
              icon="file-pdf-box"
              onPress={generatePDF}
              style={styles.modernSecondaryButton}
              contentStyle={styles.modernSecondaryButtonContent}
              labelStyle={styles.modernSecondaryButtonLabel}
              disabled={calculatedEntries.length === 0}
              buttonColor={Colors.primary}
            >
              PDF
            </Button>

            <Button
              mode="outlined"
              icon="delete-sweep"
              onPress={clearAll}
              style={styles.modernClearButton}
              contentStyle={styles.modernSecondaryButtonContent}
              labelStyle={styles.modernClearButtonLabel}
              textColor={Colors.error}
            >
              Clear
            </Button>
          </View>
        </View>

        {/* Results Display */}
        <ResultsDisplay entries={calculatedEntries} totals={totals} />

      </ScrollView>

      {/* Modern Floating Action Button */}
      <FAB
        icon="plus"
        style={styles.modernFab}
        onPress={addNewEntry}
        label="Add Entry"
        color={Colors.primary} // Green text on yellow FAB
        customSize={56}
      />

      {/* Snackbar */}
      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
        style={styles.snackbar}
        theme={{ colors: { onSurface: Colors.secondary } }}
      >
        {snackbarMessage}
      </Snackbar>

      {/* Enhanced Bottom Navigation */}
      <View style={styles.modernBottomNav}>
        {/* Gradient Top Border */}
        <LinearGradient
          colors={[Colors.primary, Colors.secondary]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.navGradientBorder}
        />

        {/* Moving Tractor Animation */}
        <Animated.View
          style={[
            styles.movingTractor,
            {
              transform: [
                {
                  translateX: tractorAnimation.interpolate({
                    inputRange: [0, 1],
                    outputRange: [-60, 420], // Move across screen
                  }),
                },
              ],
            },
          ]}
        >
          <View style={styles.tractorIcon}>
            <TractorWithTrolley size={28} color={Colors.primary} />
          </View>
        </Animated.View>

        <View style={styles.modernBottomNavContainer}>
          <View style={styles.modernNavButton}>
            <View style={styles.navIconContainer}>
              <IconButton
                icon="account-multiple"
                iconColor={Colors.primary}
                size={24}
                onPress={() => navigation.navigate('FarmerManagement')}
                style={styles.modernNavIcon}
              />
            </View>
            <Text style={styles.modernNavLabel}>Farmers</Text>
          </View>
          <View style={styles.modernNavButton}>
            <View style={styles.navIconContainer}>
              <IconButton
                icon="truck-delivery"
                iconColor={Colors.primary}
                size={24}
                onPress={() => navigation.navigate('TractorManagement')}
                style={styles.modernNavIcon}
              />
            </View>
            <Text style={styles.modernNavLabel}>Tractors</Text>
          </View>
          <View style={styles.modernNavButton}>
            <View style={styles.navIconContainer}>
              <IconButton
                icon="clock-outline"
                iconColor={Colors.primary}
                size={24}
                onPress={() => navigation.navigate('History')}
                style={styles.modernNavIcon}
              />
            </View>
            <Text style={styles.modernNavLabel}>History</Text>
          </View>
          <View style={styles.modernNavButton}>
            <View style={styles.navIconContainer}>
              <IconButton
                icon="bookmark-outline"
                iconColor={Colors.primary}
                size={24}
                onPress={() => navigation.navigate('SavedBills')}
                style={styles.modernNavIcon}
              />
            </View>
            <Text style={styles.modernNavLabel}>Saved</Text>
          </View>
          <View style={styles.modernNavButton}>
            <View style={styles.navIconContainer}>
              <IconButton
                icon="cog-outline"
                iconColor={Colors.primary}
                size={24}
                onPress={() => navigation.navigate('Settings')}
                style={styles.modernNavIcon}
              />
            </View>
            <Text style={styles.modernNavLabel}>Settings</Text>
          </View>
        </View>
      </View>

      {/* Modern Confirmation Dialog */}
      <ModernConfirmDialog
        visible={clearAllDialogVisible}
        onDismiss={() => setClearAllDialogVisible(false)}
        title="Clear All Entries"
        message="Are you sure you want to clear all entries? This action cannot be undone."
        icon="delete-sweep"
        confirmText="Clear All"
        cancelText="Cancel"
        onConfirm={handleClearAllConfirm}
        isDestructive={true}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingBottom: 100, // Reduced padding since tractor is on gradient line
  },

  // Modern Header Styles
  modernHeader: {
    backgroundColor: Colors.surface,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    zIndex: 1,
  },
  gradientHeader: {
    paddingTop: Spacing.xl + 20,
    paddingBottom: Spacing.xl,
    paddingHorizontal: Spacing.lg,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    minHeight: 140,
    marginTop: 10,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  headerRight: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  appIcon: {
    width: 48,
    height: 48,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  appIconButton: {
    margin: 0,
  },
  paddyIcon: {
    width: 56,
    height: 56,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.25)',
  },
  paddyIconButton: {
    margin: 0,
  },
  headerTextContainer: {
    flex: 1,
  },
  appTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.textLight,
    marginBottom: 2,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  appSubtitle: {
    fontSize: 14,
    color: Colors.textLight,
    fontWeight: '500',
    opacity: 0.9,
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },

  // Modern Stats Styles
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: Spacing.md,
  },
  modernStatCard: {
    flex: 1,
    backgroundColor: Colors.surface,
    borderRadius: 12,
    padding: Spacing.md,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.border,
    minWidth: 100,
    maxWidth: 120,
  },
  statValue: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.primary,
    marginBottom: 4,
    textAlign: 'center',
    flexShrink: 1,
  },
  statLabel: {
    fontSize: 12,
    color: Colors.textSecondary,
    fontWeight: '500',
    textAlign: 'center',
  },

  // Header Styles
  headerSurface: {
    backgroundColor: Colors.primary,
  },
  header: {
    backgroundColor: Colors.primary,
    elevation: 0,
  },
  headerTitle: {
    color: Colors.surface,
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
  },
  headerSubtitle: {
    color: Colors.primaryLight,
    fontSize: Typography.fontSize.sm,
    marginTop: Spacing.xs,
  },
  headerAction: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: BorderRadius.round,
  },

  // Welcome Card Styles
  welcomeCard: {
    marginHorizontal: Spacing.md,
    marginTop: Spacing.md,
    marginBottom: Spacing.sm,
    borderRadius: BorderRadius.lg,
    backgroundColor: Colors.surface,
  },
  welcomeContent: {
    paddingVertical: Spacing.md,
  },
  welcomeTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.primary,
    marginBottom: Spacing.sm,
    textAlign: 'center',
  },
  welcomeText: {
    fontSize: Typography.fontSize.sm,
    color: Colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: Spacing.md,
  },

  // Stats Styles
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: Spacing.md,
    paddingTop: Spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.primary,
  },
  statLabel: {
    fontSize: Typography.fontSize.xs,
    color: Colors.textSecondary,
    marginTop: Spacing.xs,
  },

  scrollView: {
    flex: 1,
  },
  formsContainer: {
    paddingTop: Spacing.sm,
  },

  // Action Card Styles
  actionCard: {
    marginHorizontal: Spacing.md,
    marginVertical: Spacing.sm, // Reduced margin
    borderRadius: BorderRadius.lg,
    backgroundColor: Colors.surface,
  },
  actionSectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.primary,
    marginBottom: Spacing.md,
    textAlign: 'center',
  },
  actionButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: Spacing.sm,
  },
  button: {
    minWidth: '48%',
    marginVertical: Spacing.xs,
    borderRadius: BorderRadius.lg,
  },
  buttonContent: {
    paddingVertical: Spacing.sm,
  },
  buttonLabel: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
  },
  calculateButton: {
    backgroundColor: Colors.primary,
  },
  saveButton: {
    backgroundColor: Colors.success,
  },
  pdfButton: {
    backgroundColor: Colors.info,
  },
  clearButton: {
    borderColor: Colors.error,
    borderWidth: 2,
  },
  clearButtonLabel: {
    color: Colors.error,
    fontWeight: Typography.fontWeight.medium,
  },

  // Modern Action Styles
  modernActionSection: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    gap: Spacing.md,
  },
  primaryActions: {
    marginBottom: Spacing.sm,
  },
  modernPrimaryButton: {
    borderRadius: 16,
    elevation: 2,
    shadowColor: Colors.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  modernButtonContent: {
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
  },
  modernButtonLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.secondary, // Yellow text on green button
  },
  secondaryActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: Spacing.sm,
  },
  modernSecondaryButton: {
    flex: 1,
    borderRadius: 12,
    elevation: 1,
  },
  modernSecondaryButtonContent: {
    paddingVertical: Spacing.sm,
  },
  modernSecondaryButtonLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.secondary, // Yellow text on green button
  },
  modernClearButton: {
    flex: 1,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Colors.error,
    backgroundColor: 'transparent',
  },
  modernClearButtonLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.error,
  },

  // Modern FAB Styles
  modernFab: {
    position: 'absolute',
    right: Spacing.lg,
    bottom: 120, // Properly positioned above navigation bar
    backgroundColor: Colors.secondary,
    borderRadius: 28,
    elevation: 6,
    shadowColor: Colors.secondary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },

  // Snackbar Styles
  snackbar: {
    backgroundColor: Colors.primary,
    borderRadius: BorderRadius.md,
    margin: Spacing.md,
    marginBottom: 80, // Position above bottom navigation
    elevation: 10, // Higher than bottom navigation
  },

  // Enhanced Bottom Navigation Styles
  modernBottomNav: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: Colors.surface,
    paddingBottom: 12,
    paddingTop: 15, // Reduced padding
    elevation: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'visible',
  },
  navGradientBorder: {
    height: 3,
    width: '100%',
  },
  movingTractor: {
    position: 'absolute',
    top: -18, // Position tractor on the gradient line
    zIndex: 10,
  },
  tractorIcon: {
    backgroundColor: Colors.secondary,
    borderRadius: 16,
    elevation: 6,
    shadowColor: Colors.primary,
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.4,
    shadowRadius: 6,
    width: 36,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modernBottomNavContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingTop: 12,
    paddingHorizontal: Spacing.xs,
    paddingBottom: 2,
  },
  modernNavButton: {
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 60,
    paddingVertical: 4,
  },
  navIconContainer: {
    backgroundColor: Colors.secondaryLowOpacity,
    borderRadius: 10,
    padding: 2,
    marginBottom: 2,
  },
  modernNavIcon: {
    margin: 0,
  },
  modernNavLabel: {
    fontSize: 11,
    color: Colors.primary,
    fontWeight: '600',
    textAlign: 'center',
  },
});

export default CalculatorScreen;
