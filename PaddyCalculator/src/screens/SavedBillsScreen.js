import React, { useState, useEffect, useRef } from 'react';
import { View, StyleSheet, ScrollView, RefreshControl, Alert, Share, Animated } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  Appbar,
  Card,
  Text,
  Chip,
  IconButton,
  Portal,
  Dialog,
  Button,
  FAB,
  ActivityIndicator,
  Surface
} from 'react-native-paper';
import { useFocusEffect } from '@react-navigation/native';
import TractorWithTrolley from '../components/TractorWithTrolley';
import ModernConfirmDialog from '../components/ModernConfirmDialog';
import ModernAlertDialog from '../components/ModernAlertDialog';
import { Colors, Spacing, Typography, BorderRadius } from '../constants/theme';
import { formatCurrency, formatWeight } from '../utils/calculations';
import {
  getSavedBills,
  deleteSavedBill,
  getAllFarmers,
  getAllTractors
} from '../database/database';
import * as Sharing from 'expo-sharing';

const SavedBillsScreen = ({ navigation }) => {
  const [savedBills, setSavedBills] = useState([]);
  const [farmers, setFarmers] = useState([]);
  const [tractors, setTractors] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedBill, setSelectedBill] = useState(null);
  const [dialogVisible, setDialogVisible] = useState(false);
  const [deleteDialogVisible, setDeleteDialogVisible] = useState(false);
  const [errorDialogVisible, setErrorDialogVisible] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  // Tractor Animation
  const tractorPosition = useRef(new Animated.Value(0)).current;

  // Load data when screen comes into focus
  useFocusEffect(
    React.useCallback(() => {
      loadData();
    }, [])
  );

  // Tractor Animation Effect
  useEffect(() => {
    const animateTractor = () => {
      Animated.loop(
        Animated.sequence([
          Animated.timing(tractorPosition, {
            toValue: 300,
            duration: 8000,
            useNativeDriver: true,
          }),
          Animated.timing(tractorPosition, {
            toValue: 0,
            duration: 0,
            useNativeDriver: true,
          }),
        ])
      ).start();
    };

    animateTractor();
  }, [tractorPosition]);

  const loadData = async () => {
    try {
      setLoading(true);
      const [billsData, farmersData, tractorsData] = await Promise.all([
        getSavedBills(),
        getAllFarmers(),
        getAllTractors()
      ]);
      
      setSavedBills(billsData);
      setFarmers(farmersData);
      setTractors(tractorsData);
    } catch (error) {
      
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getBuyerName = (buyerId) => {
    const buyer = farmers.find(f => f.id === buyerId && (f.type === 'buyer' || f.type === 'both'));
    return buyer ? buyer.name : 'Unknown Buyer';
  };

  const getSellerName = (sellerId) => {
    const seller = farmers.find(f => f.id === sellerId && (f.type === 'seller' || f.type === 'both'));
    return seller ? seller.name : 'Unknown Seller';
  };

  const getTractorInfo = (tractorId) => {
    const tractor = tractors.find(t => t.id === tractorId);
    return tractor ? `${tractor.vehicle_number} (${tractor.driver_name})` : 'Unknown';
  };

  const getTractorSummary = (bill) => {
    if (!bill.entries || bill.entries.length === 0) {
      return 'No entries';
    }

    const uniqueTractors = [...new Set(bill.entries.map(entry => entry.tractor_id))];
    
    if (uniqueTractors.length === 1) {
      return getTractorInfo(uniqueTractors[0]);
    } else {
      return `Multiple Tractors (${uniqueTractors.length})`;
    }
  };

  const viewBillDetails = (bill) => {
    navigation.navigate('BillDetails', {
      bill,
      farmers,
      tractors
    });
  };

  const deleteBill = async (bill) => {
    setSelectedBill(bill);
    setDeleteDialogVisible(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      await deleteSavedBill(selectedBill.id);
      await loadData(); // Refresh the list
    } catch (error) {
      
    }
  };

  const shareBill = async (bill) => {
    try {
      const buyerName = getBuyerName(bill.buyer_id);
      const sellerName = getSellerName(bill.seller_id);

      let entriesDetails = '';
      if (bill.entries && bill.entries.length > 0) {
        entriesDetails = bill.entries.map((entry, index) => {
          const tractorInfo = getTractorInfo(entry.tractor_id);
          return `
📋 Entry ${index + 1}: ${entry.bill_no || 'No Receipt'}
🚛 Tractor: ${tractorInfo}
⚖️ Gross: ${formatWeight(entry.gross_weight)}
🚚 Tractor Wt: ${formatWeight(entry.tractor_weight)}
📏 Net: ${formatWeight(entry.net_weight)}
🔄 Rewash: ${entry.rewash_percentage}%
⚖️ Adjusted: ${formatWeight(entry.adjusted_weight)}
💰 Rate: ₹${entry.price_per_900}/900kg
💵 Amount: ${formatCurrency(entry.calculated_price)}`;
        }).join('\n');
      }

      const message = `📋 Saved Paddy Bill: ${bill.generated_bill_no || bill.bill_no}
🕒 Date: ${formatDate(bill.created_at)}
👤 Buyer: ${buyerName}
🌾 Seller: ${sellerName}
📝 Total Entries: ${bill.entry_count}
⚖️ Total Weight: ${formatWeight(bill.total_weight)}
💰 Total Amount: ${formatCurrency(bill.total_amount)}

${entriesDetails}

Generated by Paddy Calculator App`;

      await Share.share({
        message: message,
        title: `Saved Bill ${bill.generated_bill_no || bill.bill_no}`
      });
    } catch (error) {
      
    }
  };

  const generateBillPDF = async (bill) => {
    try {
      if (!bill.entries || bill.entries.length === 0) {
        setErrorMessage('No entries found for this bill');
        setErrorDialogVisible(true);
        return;
      }

      // Import PDF generator
      const { generatePaddyReceiptPDF } = await import('../utils/pdfGenerator');

      // Ensure entries have the correct structure for PDF generation
      const formattedEntries = bill.entries.map(entry => ({
        ...entry,
        billNo: entry.bill_no || entry.billNo || `Entry-${Date.now()}`,
        // Ensure all required fields exist
        grossWeight: entry.gross_weight || entry.grossWeight || 0,
        tractorWeight: entry.tractor_weight || entry.tractorWeight || 0,
        netWeight: entry.net_weight || entry.netWeight || 0,
        adjustedWeight: entry.adjusted_weight || entry.adjustedWeight || 0,
        calculatedPrice: entry.calculated_price || entry.calculatedPrice || 0,
        rewashPercentage: entry.rewash_percentage || entry.rewashPercentage || 0,
        pricePer900: entry.price_per_900 || entry.pricePer900 || 0,
        tractorNo: entry.tractor_no || entry.tractorNo || '',
        tractorId: entry.tractor_id || entry.tractorId || null,
        buyerId: entry.buyer_id || entry.buyerId || bill.buyer_id,
        sellerId: entry.seller_id || entry.sellerId || bill.seller_id
      }));

      // Generate PDF with bill entries
      const pdfUri = await generatePaddyReceiptPDF(
        formattedEntries,
        {
          title: `Saved Bill ${bill.generated_bill_no || bill.bill_no}`,
          format: 'A4',
          orientation: 'portrait'
        },
        farmers,
        tractors
      );

      // Share the PDF
      await Sharing.shareAsync(pdfUri);
    } catch (error) {
      console.error('PDF Generation Error:', error);
      console.error('Error stack:', error.stack);
      setErrorMessage(`Failed to generate PDF: ${error.message}`);
      setErrorDialogVisible(true);
    }
  };

  return (
    <View style={styles.container}>
      {/* Modern Header */}
      <View style={styles.headerContainer}>
        <View style={styles.headerContent}>
          <View style={styles.headerTop}>
            <IconButton
              icon="arrow-left"
              size={24}
              iconColor={Colors.textLight}
              onPress={() => navigation.goBack()}
              style={styles.backButton}
            />
            <View style={styles.headerTitleContainer}>
              <View style={styles.headerTitleRow}>
                <IconButton
                  icon="bookmark-outline"
                  size={24}
                  iconColor={Colors.secondary}
                  style={styles.headerIcon}
                />
                <Text style={styles.headerTitle}>Saved Bills</Text>
              </View>
              <Text style={styles.headerSubtitle}>Your permanently saved records</Text>
            </View>
            <IconButton
              icon="refresh"
              size={24}
              iconColor={Colors.textLight}
              onPress={onRefresh}
              style={styles.refreshButton}
            />
          </View>
        </View>
      </View>

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[Colors.primary]}
            tintColor={Colors.primary}
          />
        }
      >
        <View style={styles.content}>
          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={Colors.primary} />
              <Text style={styles.loadingText}>Loading saved bills...</Text>
            </View>
          ) : savedBills.length === 0 ? (
            <Card style={styles.emptyCard}>
              <Card.Content style={styles.emptyContent}>
                <Text style={styles.emptyIcon}>💾</Text>
                <Text style={styles.emptyText}>No saved bills found</Text>
                <Text style={styles.emptySubtext}>
                  Save bills from the History screen to see them here
                </Text>
                <Button
                  mode="contained"
                  onPress={() => navigation.navigate('Calculator')}
                  style={styles.emptyButton}
                  icon="plus"
                >
                  Create New Bill
                </Button>
              </Card.Content>
            </Card>
          ) : (
            savedBills.map((bill) => (
              <Card key={bill.id} style={styles.billCard}>
                <Card.Content>
                  <View style={styles.billHeader}>
                    <View style={styles.billInfo}>
                      <Text style={styles.billNumber}>
                        📋 {bill.generated_bill_no || bill.bill_no}
                      </Text>
                      <Text style={styles.billDate}>
                        🕒 {formatDate(bill.created_at)}
                      </Text>
                    </View>
                    <View style={styles.billActions}>
                      <IconButton
                        icon="eye"
                        size={20}
                        iconColor={Colors.primary}
                        style={styles.actionButton}
                        onPress={() => viewBillDetails(bill)}
                      />
                      <IconButton
                        icon="share"
                        size={20}
                        iconColor={Colors.secondary}
                        style={styles.actionButton}
                        onPress={() => shareBill(bill)}
                      />
                      <IconButton
                        icon="file-pdf-box"
                        size={20}
                        iconColor={Colors.accent}
                        style={styles.actionButton}
                        onPress={() => generateBillPDF(bill)}
                      />
                      <IconButton
                        icon="delete"
                        size={20}
                        iconColor={Colors.error}
                        style={styles.actionButton}
                        onPress={() => deleteBill(bill)}
                      />
                    </View>
                  </View>

                  {/* Buyer, Seller, Tractor Info */}
                  <View style={styles.billDetails}>
                    <View style={styles.detailRow}>
                      <Text style={styles.detailLabel}>👤 Buyer:</Text>
                      <Text style={styles.detailValue}>{getBuyerName(bill.buyer_id)}</Text>
                    </View>
                    <View style={styles.detailRow}>
                      <Text style={styles.detailLabel}>🌾 Seller:</Text>
                      <Text style={styles.detailValue}>{getSellerName(bill.seller_id)}</Text>
                    </View>
                    <View style={styles.detailRow}>
                      <Text style={styles.detailLabel}>🚛 Tractor:</Text>
                      <Text style={styles.detailValue}>{getTractorSummary(bill)}</Text>
                    </View>
                  </View>

                  {/* Bill Summary Stats */}
                  <View style={styles.billStats}>
                    <Chip icon="receipt" mode="outlined" compact style={styles.statChip}>
                      {bill.entry_count} entries
                    </Chip>
                    <Chip icon="scale-balance" mode="outlined" compact style={styles.statChip}>
                      Total: {formatWeight(bill.total_weight)}
                    </Chip>
                    <Chip icon="currency-inr" mode="outlined" compact style={styles.statChip}>
                      {formatCurrency(bill.total_amount)}
                    </Chip>
                  </View>
                </Card.Content>
              </Card>
            ))
          )}
        </View>
      </ScrollView>

      {/* Bill Details Dialog */}
      <Portal>
        <Dialog visible={dialogVisible} onDismiss={() => setDialogVisible(false)}>
          <Dialog.Title style={styles.dialogTitle}>
            💾 Saved Bill: {selectedBill?.generated_bill_no || selectedBill?.bill_no}
          </Dialog.Title>
          <Dialog.ScrollArea>
            <ScrollView>
              {selectedBill && (
                <View style={styles.dialogContent}>
                  <View style={styles.dialogHeader}>
                    <Text style={styles.dialogBillNo}>
                      Bill: {selectedBill.generated_bill_no || selectedBill.bill_no}
                    </Text>
                    <Text style={styles.dialogDate}>
                      🕒 {formatDate(selectedBill.created_at)}
                    </Text>
                  </View>

                  <View style={styles.dialogParties}>
                    <View style={styles.dialogParty}>
                      <Text style={styles.dialogPartyLabel}>👤 Buyer:</Text>
                      <Text style={styles.dialogPartyValue}>{getBuyerName(selectedBill.buyer_id)}</Text>
                    </View>
                    <View style={styles.dialogParty}>
                      <Text style={styles.dialogPartyLabel}>🌾 Seller:</Text>
                      <Text style={styles.dialogPartyValue}>{getSellerName(selectedBill.seller_id)}</Text>
                    </View>
                    <View style={styles.dialogParty}>
                      <Text style={styles.dialogPartyLabel}>🚛 Tractor:</Text>
                      <Text style={styles.dialogPartyValue}>{getTractorSummary(selectedBill)}</Text>
                    </View>
                  </View>

                  <View style={styles.dialogSummary}>
                    <Text style={styles.dialogSummaryTitle}>📊 Summary</Text>
                    <View style={styles.dialogSummaryRow}>
                      <Text style={styles.dialogSummaryLabel}>Total Entries:</Text>
                      <Text style={styles.dialogSummaryValue}>{selectedBill.entry_count}</Text>
                    </View>
                    <View style={styles.dialogSummaryRow}>
                      <Text style={styles.dialogSummaryLabel}>Total Weight:</Text>
                      <Text style={styles.dialogSummaryValue}>{formatWeight(selectedBill.total_weight)}</Text>
                    </View>
                    <View style={styles.dialogSummaryRow}>
                      <Text style={styles.dialogSummaryLabel}>Total Amount:</Text>
                      <Text style={styles.dialogSummaryValue}>{formatCurrency(selectedBill.total_amount)}</Text>
                    </View>
                  </View>

                  <Text style={styles.dialogEntriesTitle}>📝 Individual Entries:</Text>
                  {selectedBill.entries?.map((entry, index) => (
                    <View key={`saved-dialog-entry-${entry.id || Math.random()}-${entry.created_at || Date.now()}-${index}`} style={styles.dialogEntry}>
                      <Text style={styles.dialogEntryTitle}>
                        📋 Receipt: {entry.bill_no || `Entry ${index + 1}`}
                      </Text>
                      <View style={styles.dialogEntryDetails}>
                        <Text style={styles.dialogEntryDetail}>
                          🚛 Tractor: {getTractorInfo(entry.tractor_id)}
                        </Text>
                        <Text style={styles.dialogEntryDetail}>
                          ⚖️ Gross: {formatWeight(entry.gross_weight)}
                        </Text>
                        <Text style={styles.dialogEntryDetail}>
                          🚚 Tractor Wt: {formatWeight(entry.tractor_weight)}
                        </Text>
                        <Text style={styles.dialogEntryDetail}>
                          📏 Net: {formatWeight(entry.net_weight)}
                        </Text>
                        <Text style={styles.dialogEntryDetail}>
                          🔄 Rewash: {entry.rewash_percentage}%
                        </Text>
                        <Text style={styles.dialogEntryDetail}>
                          ⚖️ Adjusted: {formatWeight(entry.adjusted_weight)}
                        </Text>
                        <Text style={styles.dialogEntryDetail}>
                          💰 Rate: ₹{entry.price_per_900}/900kg
                        </Text>
                        <Text style={styles.dialogEntryDetail}>
                          💵 Amount: {formatCurrency(entry.calculated_price)}
                        </Text>
                        <Text style={styles.dialogEntryDetail}>
                          📊 Yield: {(() => {
                            const yieldRatio = entry.yield_ratio || 0;
                            const numBags = entry.num_bags || 0;
                            const remainingKg = entry.remaining_kg || 0;
                            const puttiText = yieldRatio === 1 ? 'Putti' : 'Puttla';
                            const bastaText = numBags === 1 ? 'Basta' : 'Bastala';
                            return `${yieldRatio} ${puttiText} ${numBags} ${bastaText} ${remainingKg.toFixed(2)} Kg's`;
                          })()}
                        </Text>
                      </View>
                    </View>
                  ))}

                  {/* Yield Summary Section */}
                  <View style={styles.yieldSummarySection}>
                    <Text style={styles.yieldSummaryTitle}>📊 Yield Summary</Text>
                    {selectedBill.entries?.map((entry, index) => (
                      <Text key={`saved-dialog-yield-${entry.id || Math.random()}-${entry.created_at || Date.now()}-${index}`} style={styles.yieldSummaryEntry}>
                        Entry {index + 1}: {(() => {
                          const yieldRatio = entry.yield_ratio || 0;
                          const numBags = entry.num_bags || 0;
                          const remainingKg = entry.remaining_kg || 0;
                          const puttiText = yieldRatio === 1 ? 'Putti' : 'Puttla';
                          const bastaText = numBags === 1 ? 'Basta' : 'Bastala';
                          return `${yieldRatio} ${puttiText} ${numBags} ${bastaText} ${remainingKg.toFixed(2)} Kg's`;
                        })()}
                      </Text>
                    ))}
                    <Text style={styles.yieldSummaryTotal}>
                      Overall Total: {(() => {
                        const totalAdjusted = selectedBill.entries?.reduce((sum, entry) => sum + (entry.adjusted_weight || 0), 0) || 0;
                        const overallYieldRatio = Math.floor(totalAdjusted / 900);
                        const overallPaddyWeight = totalAdjusted - (overallYieldRatio * 900);
                        const overallBags = Math.floor(overallPaddyWeight / 45);
                        const overallRemainingKg = overallPaddyWeight % 45;
                        const puttiText = overallYieldRatio === 1 ? 'Putti' : 'Puttla';
                        const bastaText = overallBags === 1 ? 'Basta' : 'Bastala';
                        return `${overallYieldRatio} ${puttiText} ${overallBags} ${bastaText} ${overallRemainingKg.toFixed(2)} Kg's`;
                      })()}
                    </Text>
                  </View>
                </View>
              )}
            </ScrollView>
          </Dialog.ScrollArea>
          <Dialog.Actions style={styles.dialogActions}>
            <Button 
              mode="outlined" 
              onPress={() => shareBill(selectedBill)}
              style={styles.dialogButton}
              icon="share"
            >
              Share
            </Button>
            <Button 
              mode="contained" 
              onPress={() => generateBillPDF(selectedBill)}
              style={styles.dialogButton}
              icon="file-pdf-box"
            >
              PDF
            </Button>
            <Button 
              mode="text" 
              onPress={() => setDialogVisible(false)}
              style={styles.dialogButton}
            >
              Close
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>

      {/* FAB for new calculation */}
      <FAB
        icon="plus"
        style={styles.fab}
        onPress={() => navigation.navigate('Calculator')}
        label="New"
      />

      {/* Enhanced Bottom Navigation */}
      <View style={styles.modernBottomNav}>
        <LinearGradient
          colors={[Colors.primary, Colors.secondary]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.navGradientBorder}
        />
        <Animated.View style={[styles.movingTractor, { transform: [{ translateX: tractorPosition }] }]}>
          <View style={styles.tractorIcon}>
            <TractorWithTrolley size={28} color={Colors.primary} />
          </View>
        </Animated.View>
        <View style={styles.modernBottomNavContainer}>
          <View style={styles.modernNavButton}>
            <View style={styles.navIconContainer}>
              <IconButton
                icon="calculator"
                iconColor={Colors.primary}
                size={24}
                onPress={() => navigation.navigate('Calculator')}
                style={styles.modernNavIcon}
              />
            </View>
            <Text style={styles.modernNavLabel}>Calculator</Text>
          </View>
          <View style={styles.modernNavButton}>
            <View style={styles.navIconContainer}>
              <IconButton
                icon="account-multiple"
                iconColor={Colors.primary}
                size={24}
                onPress={() => navigation.navigate('FarmerManagement')}
                style={styles.modernNavIcon}
              />
            </View>
            <Text style={styles.modernNavLabel}>Farmers</Text>
          </View>
          <View style={styles.modernNavButton}>
            <View style={styles.navIconContainer}>
              <IconButton
                icon="truck-delivery"
                iconColor={Colors.primary}
                size={24}
                onPress={() => navigation.navigate('TractorManagement')}
                style={styles.modernNavIcon}
              />
            </View>
            <Text style={styles.modernNavLabel}>Tractors</Text>
          </View>
          <View style={styles.modernNavButton}>
            <View style={styles.navIconContainer}>
              <IconButton
                icon="clock-outline"
                iconColor={Colors.primary}
                size={24}
                onPress={() => navigation.navigate('History')}
                style={styles.modernNavIcon}
              />
            </View>
            <Text style={styles.modernNavLabel}>History</Text>
          </View>
          <View style={[styles.modernNavButton, styles.activeNavButton]}>
            <View style={[styles.navIconContainer, styles.activeNavIconContainer]}>
              <IconButton
                icon="bookmark-outline"
                iconColor={Colors.secondary}
                size={24}
                onPress={() => navigation.navigate('SavedBills')}
                style={styles.modernNavIcon}
              />
            </View>
            <Text style={[styles.modernNavLabel, styles.activeNavLabel]}>Saved</Text>
          </View>
        </View>
      </View>

      {/* Modern Delete Confirmation Dialog */}
      <ModernConfirmDialog
        visible={deleteDialogVisible}
        onDismiss={() => setDeleteDialogVisible(false)}
        title="Delete Bill"
        message={`Are you sure you want to delete bill ${selectedBill?.generated_bill_no || selectedBill?.bill_no}?`}
        icon="delete"
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={handleDeleteConfirm}
        isDestructive={true}
      />

      {/* Modern Error Dialog */}
      <ModernAlertDialog
        visible={errorDialogVisible}
        onDismiss={() => setErrorDialogVisible(false)}
        title="Error"
        message={errorMessage}
        icon="alert-circle"
        buttonText="OK"
        isError={true}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    paddingBottom: 100, // Reduced padding since tractor is on gradient line
  },
  // Modern Header Styles
  headerContainer: {
    backgroundColor: Colors.primary,
    paddingTop: 50,
    paddingBottom: Spacing.lg,
    borderBottomLeftRadius: BorderRadius.xl,
    borderBottomRightRadius: BorderRadius.xl,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  headerContent: {
    paddingHorizontal: Spacing.lg,
  },
  headerTop: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    margin: 0,
  },
  refreshButton: {
    margin: 0,
  },
  headerTitleContainer: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerIcon: {
    margin: 0,
    marginRight: 8,
  },
  headerTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.secondary, // Yellow text like TractorManagement
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: Typography.fontSize.sm,
    color: Colors.secondary, // Yellow text like TractorManagement
    opacity: 0.9,
    textAlign: 'center',
    marginTop: 2,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: Spacing.md,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.xl * 2,
  },
  loadingText: {
    textAlign: 'center',
    fontSize: Typography.fontSize.md,
    color: Colors.textSecondary,
    marginTop: Spacing.md,
  },
  emptyCard: {
    marginTop: Spacing.xl,
    elevation: 2,
    borderRadius: BorderRadius.xl,
  },
  emptyContent: {
    alignItems: 'center',
    paddingVertical: Spacing.xl,
  },
  emptyIcon: {
    fontSize: 48,
    marginBottom: Spacing.md,
  },
  emptyText: {
    textAlign: 'center',
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.textSecondary,
    marginBottom: Spacing.sm,
  },
  emptySubtext: {
    textAlign: 'center',
    fontSize: Typography.fontSize.sm,
    color: Colors.textSecondary,
    marginBottom: Spacing.lg,
  },
  emptyButton: {
    marginTop: Spacing.md,
  },
  billCard: {
    marginBottom: Spacing.md,
    borderRadius: BorderRadius.lg,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    backgroundColor: Colors.surface,
    borderWidth: 1,
    borderColor: Colors.primary + '10',
  },
  billHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: Spacing.md,
  },
  billInfo: {
    flex: 1,
  },
  billNumber: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.primary,
    marginBottom: Spacing.xs,
    letterSpacing: 0.5,
  },
  billDate: {
    fontSize: Typography.fontSize.sm,
    color: Colors.textSecondary,
  },
  billActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    margin: 0,
    marginLeft: Spacing.xs,
    borderRadius: 20,
    backgroundColor: Colors.surface,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  billDetails: {
    marginBottom: Spacing.md,
    backgroundColor: '#f8f9fa',
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  detailLabel: {
    fontSize: Typography.fontSize.sm,
    color: Colors.textSecondary,
    fontWeight: Typography.fontWeight.medium,
  },
  detailValue: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text,
    fontWeight: Typography.fontWeight.medium,
    flex: 1,
    textAlign: 'right',
  },
  billStats: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.sm,
    marginTop: Spacing.sm,
  },
  statChip: {
    marginRight: Spacing.xs,
    marginBottom: Spacing.xs,
    backgroundColor: Colors.primaryLight,
    borderColor: Colors.primary + '30',
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
    backgroundColor: Colors.secondary,
  },
  // Dialog styles (reused from HistoryScreen)
  dialogTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.primary,
    textAlign: 'center',
  },
  dialogContent: {
    padding: Spacing.md,
  },
  dialogHeader: {
    marginBottom: Spacing.md,
    paddingBottom: Spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  dialogBillNo: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.primary,
    marginBottom: Spacing.xs,
  },
  dialogDate: {
    fontSize: Typography.fontSize.sm,
    color: Colors.textSecondary,
    fontStyle: 'italic',
  },
  dialogParties: {
    marginBottom: Spacing.md,
    padding: Spacing.md,
    backgroundColor: '#f8f9fa',
    borderRadius: BorderRadius.md,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  dialogParty: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
    paddingVertical: Spacing.xs,
  },
  dialogPartyLabel: {
    fontSize: Typography.fontSize.md,
    color: Colors.textSecondary,
    fontWeight: Typography.fontWeight.semibold,
    minWidth: 80,
  },
  dialogPartyValue: {
    fontSize: Typography.fontSize.md,
    color: Colors.text,
    fontWeight: Typography.fontWeight.medium,
    flex: 1,
    textAlign: 'right',
  },
  dialogSummary: {
    marginBottom: Spacing.md,
    padding: Spacing.md,
    backgroundColor: Colors.primaryLight,
    borderRadius: BorderRadius.md,
    borderWidth: 1,
    borderColor: Colors.primary + '20',
  },
  dialogSummaryTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.primary,
    marginBottom: Spacing.md,
    textAlign: 'center',
  },
  dialogSummaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  dialogSummaryLabel: {
    fontSize: Typography.fontSize.sm,
    color: Colors.textSecondary,
    fontWeight: Typography.fontWeight.medium,
  },
  dialogSummaryValue: {
    fontSize: Typography.fontSize.sm,
    color: Colors.primary,
    fontWeight: Typography.fontWeight.bold,
  },
  dialogActions: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    justifyContent: 'space-around',
  },
  dialogButton: {
    marginHorizontal: Spacing.xs,
  },
  dialogEntriesTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.primary,
    marginTop: Spacing.lg,
    marginBottom: Spacing.md,
    textAlign: 'center',
  },
  dialogEntry: {
    backgroundColor: '#f8f9fa',
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    marginBottom: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  dialogEntryTitle: {
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.primary,
    marginBottom: Spacing.sm,
  },
  dialogEntryDetails: {
    gap: Spacing.xs,
  },
  dialogEntryDetail: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text,
    paddingVertical: 2,
  },
  yieldSummarySection: {
    marginTop: Spacing.lg,
    padding: Spacing.md,
    backgroundColor: '#f0f8f0',
    borderRadius: BorderRadius.md,
    borderWidth: 1,
    borderColor: '#4CAF50',
  },
  yieldSummaryTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
    color: '#2E7D32',
    marginBottom: Spacing.sm,
    textAlign: 'center',
  },
  yieldSummaryEntry: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text,
    marginBottom: Spacing.xs,
    paddingLeft: Spacing.sm,
  },
  yieldSummaryTotal: {
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.bold,
    color: '#2E7D32',
    marginTop: Spacing.sm,
    paddingTop: Spacing.sm,
    borderTopWidth: 1,
    borderTopColor: '#4CAF50',
    textAlign: 'center',
  },

  // Enhanced Bottom Navigation Styles
  modernBottomNav: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: Colors.surface,
    paddingBottom: 12,
    paddingTop: 15, // Reduced padding
    elevation: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'visible',
  },
  navGradientBorder: {
    height: 3,
    width: '100%',
  },
  movingTractor: {
    position: 'absolute',
    top: -18, // Position tractor on the gradient line
    zIndex: 10,
  },
  tractorIcon: {
    backgroundColor: Colors.secondary,
    borderRadius: 16,
    elevation: 6,
    shadowColor: Colors.primary,
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.4,
    shadowRadius: 6,
    width: 36,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modernBottomNavContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingTop: 12,
    paddingHorizontal: Spacing.xs,
    paddingBottom: 2,
  },
  modernNavButton: {
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 60,
    paddingVertical: 4,
  },
  navIconContainer: {
    backgroundColor: Colors.secondaryLowOpacity,
    borderRadius: 10,
    padding: 2,
    marginBottom: 2,
  },
  activeNavButton: {
    // Active button styling
  },
  activeNavIconContainer: {
    backgroundColor: Colors.primary,
  },
  modernNavIcon: {
    margin: 0,
  },
  modernNavLabel: {
    fontSize: 11,
    color: Colors.primary,
    fontWeight: '600',
    textAlign: 'center',
  },
  activeNavLabel: {
    color: Colors.secondary,
    fontWeight: '700',
  },
});

export default SavedBillsScreen;
