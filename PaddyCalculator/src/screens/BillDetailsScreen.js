import React from 'react';
import { View, StyleSheet, ScrollView, Share, Alert } from 'react-native';
import * as Sharing from 'expo-sharing';
import {
  Appbar,
  Card,
  Text,
  Button,
  Chip,
  IconButton
} from 'react-native-paper';
import { Colors, Spacing, Typography, BorderRadius } from '../constants/theme';
import { formatCurrency, formatWeight, formatYieldDisplay } from '../utils/calculations';

const BillDetailsScreen = ({ route, navigation }) => {
  const { bill, farmers = [], tractors = [] } = route.params;

  const getBuyerName = (buyerId) => {
    const buyer = farmers.find(f => f.id === buyerId && (f.type === 'buyer' || f.type === 'both'));
    return buyer ? buyer.name : 'Unknown Buyer';
  };

  const getSellerName = (sellerId) => {
    const seller = farmers.find(f => f.id === sellerId && (f.type === 'seller' || f.type === 'both'));
    return seller ? seller.name : 'Unknown Seller';
  };

  const shareBill = async () => {
    try {
      let shareText = `📋 Bill Details: ${bill.generated_bill_no || bill.bill_no}\n`;
      shareText += `📅 Date: ${new Date(bill.created_at || Date.now()).toLocaleDateString()}\n\n`;

      shareText += `👤 Buyer: ${getBuyerName(bill.buyer_id)}\n`;
      shareText += `🌾 Seller: ${getSellerName(bill.seller_id)}\n\n`;

      shareText += `📊 Summary:\n`;
      shareText += `• Total Entries: ${bill.entryCount || bill.entries?.length || 0}\n`;
      shareText += `• Total Weight: ${formatWeight(bill.totalWeight || bill.total_weight || 0)}\n`;
      shareText += `• Total Amount: ${formatCurrency(bill.totalAmount || bill.total_amount || 0)}\n\n`;

      if (bill.entries && bill.entries.length > 0) {
        shareText += `📝 Individual Entries:\n`;
        bill.entries.forEach((entry, index) => {
          shareText += `\n📋 Entry ${index + 1}: ${entry.bill_no || 'No Receipt'}\n`;
          shareText += `⚖️ Gross: ${formatWeight(entry.gross_weight)}\n`;
          shareText += `🚚 Tractor Wt: ${formatWeight(entry.tractor_weight)}\n`;
          shareText += `📏 Net: ${formatWeight(entry.net_weight)}\n`;
          shareText += `🔄 Rewash: ${entry.rewash_percentage}%\n`;
          shareText += `⚖️ Adjusted: ${formatWeight(entry.adjusted_weight)}\n`;
          shareText += `💰 Rate: ₹${entry.price_per_900}/900kg\n`;
          shareText += `💵 Amount: ${formatCurrency(entry.calculated_price)}\n`;

          // Add yield information
          shareText += `📊 Yield: ${formatYieldDisplay(
            entry.yield_ratio || 0,
            entry.num_bags || 0,
            entry.remaining_kg || 0
          )}\n`;
        });
      }

      const result = await Share.share({
        message: shareText,
        title: `Bill Details: ${bill.generated_bill_no || bill.bill_no}`,
      });

      if (result.action === Share.sharedAction) {
        
      } else if (result.action === Share.dismissedAction) {
        
      }
    } catch (error) {
      
      // You could show an alert to the user here if needed
    }
  };

  const generateBillPDF = async () => {
    try {
      if (!bill.entries || bill.entries.length === 0) {
        
        return;
      }

      

      // Import PDF generator
      const { generatePaddyReceiptPDF } = await import('../utils/pdfGenerator');

      // Ensure entries have the correct structure for PDF generation
      const formattedEntries = bill.entries.map(entry => ({
        ...entry,
        billNo: entry.bill_no || entry.billNo || `Entry-${Date.now()}`,
        // Ensure all required fields exist
        grossWeight: entry.gross_weight || entry.grossWeight || 0,
        tractorWeight: entry.tractor_weight || entry.tractorWeight || 0,
        netWeight: entry.net_weight || entry.netWeight || 0,
        adjustedWeight: entry.adjusted_weight || entry.adjustedWeight || 0,
        calculatedPrice: entry.calculated_price || entry.calculatedPrice || 0,
        pricePer900: entry.price_per_900 || entry.pricePer900 || 0,
        rewashPercentage: entry.rewash_percentage || entry.rewashPercentage || 0,
        tractorNo: entry.tractor_no || entry.tractorNo || '',
        tractorId: entry.tractor_id || entry.tractorId || null,
        buyerId: entry.buyer_id || entry.buyerId || bill.buyer_id,
        sellerId: entry.seller_id || entry.sellerId || bill.seller_id,
        // Add yield-related fields for PDF
        yieldRatio: entry.yield_ratio || entry.yieldRatio || 0,
        numBags: entry.num_bags || entry.numBags || 0,
        remainingKg: entry.remaining_kg || entry.remainingKg || 0,
        wasteWeight: entry.waste_weight || entry.wasteWeight || 0
      }));

      // Generate PDF with bill entries
      const pdfUri = await generatePaddyReceiptPDF(
        formattedEntries,
        {
          title: `Bill ${bill.generated_bill_no || bill.bill_no}`,
          format: 'A4',
          orientation: 'portrait'
        },
        farmers,
        tractors
      );

      

      // Share the PDF
      await Sharing.shareAsync(pdfUri);

    } catch (error) {
      console.error('PDF Generation Error:', error);
      console.error('Error stack:', error.stack);
      Alert.alert('Error', `Failed to generate PDF: ${error.message}`);
    }
  };

  const saveBillPermanently = async () => {
    try {
      const { saveBill } = await import('../database/database');
      const { Alert } = await import('react-native');

      const billData = {
        bill_no: bill.generated_bill_no || bill.bill_no,
        buyer_id: bill.buyer_id,
        seller_id: bill.seller_id,
        total_amount: bill.totalAmount || bill.total_amount || 0,
        total_weight: bill.totalWeight || bill.total_weight || 0,
        entry_count: bill.entryCount || bill.entries?.length || 0,
        entries: bill.entries || []
      };

      const savedBillId = await saveBill(billData);
      

      Alert.alert(
        'Success',
        `Bill ${bill.generated_bill_no || bill.bill_no} saved successfully!`,
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } catch (error) {
      

      const { Alert } = await import('react-native');
      if (error.message.includes('already exists')) {
        Alert.alert(
          'Bill Already Saved',
          `Bill ${bill.generated_bill_no || bill.bill_no} has already been saved permanently.`,
          [{ text: 'OK' }]
        );
      } else {
        Alert.alert(
          'Error',
          'Failed to save bill. Please try again.',
          [{ text: 'OK' }]
        );
      }
    }
  };

  return (
    <View style={styles.container}>
      {/* Modern Header */}
      <View style={styles.headerContainer}>
        <View style={styles.headerContent}>
          <View style={styles.headerTop}>
            <IconButton
              icon="arrow-left"
              size={24}
              iconColor={Colors.textLight}
              onPress={() => navigation.goBack()}
              style={styles.backButton}
            />
            <View style={styles.headerTitleContainer}>
              <Text style={styles.headerTitle}>📋 Bill Details</Text>
              <Text style={styles.headerSubtitle}>{bill.generated_bill_no || bill.bill_no}</Text>
            </View>
          </View>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Header Info */}
        <Card style={styles.headerCard}>
          <Card.Content>
            <Text style={styles.billTitle}>
              📋 Bill: {bill.generated_bill_no || bill.bill_no}
            </Text>
            <Text style={styles.dateText}>
              📅 {new Date(bill.created_at || Date.now()).toLocaleDateString('en-IN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
              })}
            </Text>
          </Card.Content>
        </Card>

        {/* Parties Info */}
        <Card style={styles.partiesCard}>
          <Card.Content>
            <View style={styles.partyRow}>
              <Text style={styles.partyLabel}>👤 Buyer:</Text>
              <Text style={styles.partyValue}>{getBuyerName(bill.buyer_id)}</Text>
            </View>
            <View style={styles.partyRow}>
              <Text style={styles.partyLabel}>🌾 Seller:</Text>
              <Text style={styles.partyValue}>{getSellerName(bill.seller_id)}</Text>
            </View>
          </Card.Content>
        </Card>

        {/* Summary */}
        <Card style={styles.summaryCard}>
          <Card.Content>
            <Text style={styles.summaryTitle}>📊 Summary</Text>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Total Entries:</Text>
              <Text style={styles.summaryValue}>{bill.entryCount || bill.entries?.length || 0}</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Total Weight:</Text>
              <Text style={styles.summaryValue}>{formatWeight(bill.totalWeight || bill.total_weight || 0)}</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Total Amount:</Text>
              <Text style={styles.summaryValue}>{formatCurrency(bill.totalAmount || bill.total_amount || 0)}</Text>
            </View>
          </Card.Content>
        </Card>

        {/* Individual Entries */}
        <Text style={styles.entriesTitle}>📝 Individual Entries:</Text>
        {bill.entries?.map((entry, index) => (
          <Card key={entry.id || index} style={styles.entryCard}>
            <Card.Content>
              <Text style={styles.entryTitle}>
                📋 Receipt: {entry.bill_no || `Entry ${index + 1}`}
              </Text>
              <View style={styles.entryDetails}>
                <Text style={styles.entryDetail}>
                  🚛 Tractor: {entry.tractor_no}
                </Text>
                <Text style={styles.entryDetail}>
                  ⚖️ Gross: {formatWeight(entry.gross_weight)}
                </Text>
                <Text style={styles.entryDetail}>
                  🚚 Tractor Wt: {formatWeight(entry.tractor_weight)}
                </Text>
                <Text style={styles.entryDetail}>
                  📏 Net: {formatWeight(entry.net_weight)}
                </Text>
                <Text style={styles.entryDetail}>
                  🔄 Rewash: {entry.rewash_percentage}%
                </Text>
                <Text style={styles.entryDetail}>
                  ⚖️ Adjusted: {formatWeight(entry.adjusted_weight)}
                </Text>
                <Text style={styles.entryDetail}>
                  💰 Rate: ₹{entry.price_per_900}/900kg
                </Text>
                <Text style={styles.entryDetail}>
                  💵 Amount: {formatCurrency(entry.calculated_price)}
                </Text>
                <Text style={styles.entryDetail}>
                  📊 Yield: {formatYieldDisplay(
                    entry.yield_ratio || 0,
                    entry.num_bags || 0,
                    entry.remaining_kg || 0
                  )}
                </Text>
              </View>
            </Card.Content>
          </Card>
        ))}

        {/* Yield Summary Section */}
        <Card style={styles.yieldSummaryCard}>
          <Card.Content>
            <Text style={styles.yieldSummaryTitle}>📊 Yield Summary</Text>
            {bill.entries?.map((entry, index) => (
              <Text key={entry.id || index} style={styles.yieldSummaryEntry}>
                Entry {index + 1}: {formatYieldDisplay(
                  entry.yield_ratio || 0,
                  entry.num_bags || 0,
                  entry.remaining_kg || 0
                )}
              </Text>
            ))}
            <Text style={styles.yieldSummaryTotal}>
              Overall Total: {(() => {
                const totalAdjusted = bill.entries?.reduce((sum, entry) => sum + (entry.adjusted_weight || 0), 0) || 0;
                const overallYieldRatio = Math.floor(totalAdjusted / 900);
                const overallPaddyWeight = totalAdjusted - (overallYieldRatio * 900);
                const overallBags = Math.floor(overallPaddyWeight / 45);
                const overallRemainingKg = overallPaddyWeight % 45;
                return formatYieldDisplay(overallYieldRatio, overallBags, overallRemainingKg);
              })()}
            </Text>
          </Card.Content>
        </Card>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <View style={styles.buttonRow}>
            <Button
              mode="outlined"
              onPress={shareBill}
              style={styles.actionButton}
              icon="share"
            >
              Share
            </Button>
            <Button
              mode="contained"
              onPress={generateBillPDF}
              style={styles.actionButton}
              icon="file-pdf-box"
            >
              PDF
            </Button>
          </View>
          <View style={styles.buttonRow}>
            <Button
              mode="contained-tonal"
              onPress={saveBillPermanently}
              style={styles.actionButton}
              icon="content-save"
            >
              Save Bill
            </Button>
            <Button
              mode="text"
              onPress={() => navigation.goBack()}
              style={styles.actionButton}
            >
              Close
            </Button>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  // Modern Header Styles
  headerContainer: {
    backgroundColor: Colors.primary,
    paddingTop: 50,
    paddingBottom: Spacing.lg,
    borderBottomLeftRadius: BorderRadius.xl,
    borderBottomRightRadius: BorderRadius.xl,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  headerContent: {
    paddingHorizontal: Spacing.lg,
  },
  headerTop: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    margin: 0,
  },
  headerTitleContainer: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.textLight,
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: Typography.fontSize.sm,
    color: Colors.textLight,
    opacity: 0.9,
    textAlign: 'center',
    marginTop: 2,
  },
  content: {
    flex: 1,
    padding: Spacing.md,
  },
  headerCard: {
    marginBottom: Spacing.md,
    backgroundColor: '#ffffff',
  },
  billTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.primary,
    marginBottom: Spacing.xs,
  },
  dateText: {
    fontSize: Typography.fontSize.sm,
    color: Colors.textSecondary,
  },
  partiesCard: {
    marginBottom: Spacing.md,
    backgroundColor: '#ffffff',
  },
  partyRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  partyLabel: {
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.medium,
    color: Colors.text,
  },
  partyValue: {
    fontSize: Typography.fontSize.md,
    color: Colors.primary,
    fontWeight: Typography.fontWeight.bold,
  },
  summaryCard: {
    marginBottom: Spacing.md,
    backgroundColor: Colors.success + '20',
    borderWidth: 1,
    borderColor: Colors.success,
  },
  summaryTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.success,
    marginBottom: Spacing.sm,
    textAlign: 'center',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  summaryLabel: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text,
    fontWeight: Typography.fontWeight.medium,
  },
  summaryValue: {
    fontSize: Typography.fontSize.sm,
    color: Colors.primary,
    fontWeight: Typography.fontWeight.bold,
  },
  entriesTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.primary,
    marginBottom: Spacing.md,
    textAlign: 'center',
  },
  entryCard: {
    marginBottom: Spacing.md,
    backgroundColor: '#ffffff',
    borderWidth: 1,
    borderColor: Colors.border,
  },
  entryTitle: {
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.primary,
    marginBottom: Spacing.sm,
    paddingBottom: Spacing.xs,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  entryDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.sm,
  },
  entryDetail: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text,
    backgroundColor: '#f8f9fa',
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.sm,
    borderWidth: 1,
    borderColor: Colors.border,
    fontWeight: Typography.fontWeight.medium,
  },
  yieldSummaryCard: {
    marginBottom: Spacing.md,
    backgroundColor: '#f0f8f0',
    borderWidth: 1,
    borderColor: '#4CAF50',
  },
  yieldSummaryTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
    color: '#2E7D32',
    marginBottom: Spacing.sm,
    textAlign: 'center',
  },
  yieldSummaryEntry: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text,
    marginBottom: Spacing.xs,
    paddingLeft: Spacing.sm,
  },
  yieldSummaryTotal: {
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.bold,
    color: '#2E7D32',
    marginTop: Spacing.sm,
    paddingTop: Spacing.sm,
    borderTopWidth: 1,
    borderTopColor: '#4CAF50',
    textAlign: 'center',
  },
  actionButtons: {
    marginTop: Spacing.md,
    marginBottom: Spacing.xl,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: Spacing.sm,
    marginBottom: Spacing.sm,
  },
  actionButton: {
    flex: 1,
  },
});

export default BillDetailsScreen;
