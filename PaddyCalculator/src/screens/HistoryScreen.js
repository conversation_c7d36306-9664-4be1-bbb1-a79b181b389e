import React, { useState, useEffect, useCallback } from 'react';
import { View, StyleSheet, ScrollView, RefreshControl, TouchableOpacity, Share, Animated, Alert } from 'react-native';
import * as Sharing from 'expo-sharing';
import { LinearGradient } from 'expo-linear-gradient';
import {
  Appbar,
  Card,
  Text,
  Chip,
  IconButton,
  Searchbar,
  Portal,
  Dialog,
  Button,
  Menu,
  Divider,
  FAB,
  Surface
} from 'react-native-paper';
import { useFocusEffect } from '@react-navigation/native';
import TractorWithTrolley from '../components/TractorWithTrolley';
import { Colors, Spacing, Typography, BorderRadius } from '../constants/theme';
import { formatCurrency, formatWeight } from '../utils/calculations';
import {
  getAllSessions,
  getSessionWithEntries,
  getAllPaddyEntries,
  getAllFarmers,
  getAllTractors,
  saveBill,
  getSavedBills,
  deleteSavedBill
} from '../database/database';

const HistoryScreen = ({ navigation }) => {
  const [sessions, setSessions] = useState([]);
  const [entries, setEntries] = useState([]);
  const [allEntries, setAllEntries] = useState([]);
  const [farmers, setFarmers] = useState([]);
  const [tractors, setTractors] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSession, setSelectedSession] = useState(null);
  const [dialogVisible, setDialogVisible] = useState(false);

  // Filter states
  const [selectedBuyer, setSelectedBuyer] = useState(null);
  const [selectedSeller, setSelectedSeller] = useState(null);
  const [selectedTractor, setSelectedTractor] = useState(null);
  const [selectedDate, setSelectedDate] = useState(null);
  const [showDatePicker, setShowDatePicker] = useState(false);

  // Menu states
  const [buyerMenuVisible, setBuyerMenuVisible] = useState(false);
  const [sellerMenuVisible, setSellerMenuVisible] = useState(false);
  const [tractorMenuVisible, setTractorMenuVisible] = useState(false);

  // Summary data
  const [summary, setSummary] = useState({
    totalEntries: 0,
    totalAmount: 0,
    totalWeight: 0,
    avgPrice: 0
  });

  // Tractor animation
  const tractorAnimation = useState(new Animated.Value(0))[0];

  // Load data when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [])
  );

  const loadData = async () => {
    try {
      setLoading(true);
      const [sessionsData, entriesData, farmersData, tractorsData] = await Promise.all([
        getAllSessions(),
        getAllPaddyEntries(),
        getAllFarmers(),
        getAllTractors()
      ]);

      // Debug logging
      
      
      
      
      

      setSessions(sessionsData);
      setAllEntries(entriesData);
      setEntries(entriesData);
      setFarmers(farmersData);
      setTractors(tractorsData);

      // Calculate initial summary
      calculateSummary(entriesData);
    } catch (error) {
      
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  // Calculate summary for filtered entries
  const calculateSummary = (entriesData) => {
    const totalEntries = entriesData.length;
    const totalAmount = entriesData.reduce((sum, entry) => {
      const price = entry.calculated_price;
      return sum + (price && !isNaN(price) ? price : 0);
    }, 0);
    const totalWeight = entriesData.reduce((sum, entry) => {
      const weight = entry.adjusted_weight;
      return sum + (weight && !isNaN(weight) ? weight : 0);
    }, 0);
    const avgPrice = totalEntries > 0 ? totalAmount / totalEntries : 0;

    setSummary({
      totalEntries: totalEntries || 0,
      totalAmount: totalAmount || 0,
      totalWeight: totalWeight || 0,
      avgPrice: avgPrice || 0
    });
  };

  // Group entries by generated bill number
  const groupEntriesByBill = (entriesData) => {
    if (!entriesData || entriesData.length === 0) {
      return [];
    }

    const grouped = entriesData.reduce((acc, entry) => {
      // Use generated_bill_no if available, otherwise fall back to bill_no for old entries
      const billNo = entry.generated_bill_no || entry.bill_no;
      if (!acc[billNo]) {
        acc[billNo] = [];
      }
      acc[billNo].push(entry);
      return acc;
    }, {});

    // Convert to array of bill objects with summary
    return Object.keys(grouped).map(billNo => {
      const billEntries = grouped[billNo];
      const totalAmount = billEntries.reduce((sum, entry) => {
        const price = entry.calculated_price;
        return sum + (price && !isNaN(price) ? price : 0);
      }, 0);
      const totalWeight = billEntries.reduce((sum, entry) => {
        const weight = entry.adjusted_weight;
        return sum + (weight && !isNaN(weight) ? weight : 0);
      }, 0);
      const firstEntry = billEntries[0];

      return {
        bill_no: billNo,
        generated_bill_no: firstEntry.generated_bill_no,
        entries: billEntries || [],
        entryCount: billEntries.length,
        totalAmount: totalAmount || 0,
        totalWeight: totalWeight || 0,
        created_at: firstEntry.created_at,
        buyer_id: firstEntry.buyer_id,
        seller_id: firstEntry.seller_id,
        tractor_id: firstEntry.tractor_id,
        // For display purposes, use first entry's details
        ...firstEntry
      };
    });
  };

  // Filter entries based on selected criteria
  const applyFilters = useCallback(() => {
    let filteredEntries = [...allEntries];

    // Filter by buyer
    if (selectedBuyer) {
      filteredEntries = filteredEntries.filter(entry => entry.buyer_id === selectedBuyer.id);
    }

    // Filter by seller
    if (selectedSeller) {
      filteredEntries = filteredEntries.filter(entry => entry.seller_id === selectedSeller.id);
    }

    // Filter by tractor
    if (selectedTractor) {
      filteredEntries = filteredEntries.filter(entry => entry.tractor_id === selectedTractor.id);
    }

    // Filter by date
    if (selectedDate) {
      const selectedDateStr = selectedDate.toISOString().split('T')[0];
      filteredEntries = filteredEntries.filter(entry => {
        const entryDate = new Date(entry.created_at).toISOString().split('T')[0];
        return entryDate === selectedDateStr;
      });
    }

    // Filter by search query
    if (searchQuery) {
      filteredEntries = filteredEntries.filter(entry =>
        entry.bill_no.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (entry.generated_bill_no && entry.generated_bill_no.toLowerCase().includes(searchQuery.toLowerCase())) ||
        entry.tractor_no.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Group filtered entries by bill
    const groupedBills = groupEntriesByBill(filteredEntries);
    setEntries(groupedBills);
    calculateSummary(filteredEntries);
  }, [allEntries, selectedBuyer, selectedSeller, selectedTractor, selectedDate, searchQuery]);

  // Apply filters when criteria change
  useEffect(() => {
    applyFilters();
  }, [applyFilters]);

  // Start tractor animation
  useEffect(() => {
    const startTractorAnimation = () => {
      Animated.loop(
        Animated.timing(tractorAnimation, {
          toValue: 1,
          duration: 8000, // 8 seconds for full cycle
          useNativeDriver: true,
        })
      ).start();
    };

    startTractorAnimation();
  }, []);

  const clearFilters = () => {
    setSelectedBuyer(null);
    setSelectedSeller(null);
    setSelectedTractor(null);
    setSelectedDate(null);
    setSearchQuery('');
  };

  const handleSessionPress = async (session) => {
    try {
      const sessionWithEntries = await getSessionWithEntries(session.id);
      setSelectedSession(sessionWithEntries);
      setDialogVisible(true);
    } catch (error) {
      
    }
  };

  const handleDateChange = (event, date) => {
    setShowDatePicker(false);
    if (date) {
      setSelectedDate(date);
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getBuyerName = (buyerId) => {
    const buyer = farmers.find(f => f.id === buyerId && (f.type === 'buyer' || f.type === 'both'));
    return buyer ? buyer.name : 'Unknown Buyer';
  };

  const getSellerName = (sellerId) => {
    const seller = farmers.find(f => f.id === sellerId && (f.type === 'seller' || f.type === 'both'));
    return seller ? seller.name : 'Unknown Seller';
  };

  const getTractorInfo = (tractorId) => {
    const tractor = tractors.find(t => t.id === tractorId);
    return tractor ? `${tractor.vehicle_number} (${tractor.driver_name})` : 'Unknown';
  };

  const getTractorSummary = (bill) => {
    if (!bill.entries || bill.entries.length === 0) {
      return getTractorInfo(bill.tractor_id);
    }

    // Get unique tractors from all entries
    const uniqueTractors = [...new Set(bill.entries.map(entry => entry.tractor_id))];

    if (uniqueTractors.length === 1) {
      return getTractorInfo(uniqueTractors[0]);
    } else {
      return `Multiple Tractors (${uniqueTractors.length})`;
    }
  };

  const viewBillDetails = (bill) => {
    
    navigation.navigate('BillDetails', {
      bill,
      farmers,
      tractors
    });
  };

  const shareBill = async (bill) => {
    try {
      const buyerName = getBuyerName(bill.buyer_id);
      const sellerName = getSellerName(bill.seller_id);

      let entriesDetails = '';
      if (bill.entries && bill.entries.length > 0) {
        entriesDetails = bill.entries.map((entry, index) => {
          const tractorInfo = getTractorInfo(entry.tractor_id);
          return `
📋 Entry ${index + 1}: ${entry.bill_no || 'No Receipt'}
🚛 Tractor: ${tractorInfo}
⚖️ Gross: ${formatWeight(entry.gross_weight)}
🚚 Tractor Wt: ${formatWeight(entry.tractor_weight)}
📏 Net: ${formatWeight(entry.net_weight)}
🔄 Rewash: ${entry.rewash_percentage}%
⚖️ Adjusted: ${formatWeight(entry.adjusted_weight)}
💰 Rate: ₹${entry.price_per_900}/900kg
💵 Amount: ${formatCurrency(entry.calculated_price)}`;
        }).join('\n');
      }

      const message = `📋 Paddy Bill: ${bill.generated_bill_no || bill.bill_no}
🕒 Date: ${formatDate(bill.created_at)}
👤 Buyer: ${buyerName}
🌾 Seller: ${sellerName}
📝 Total Entries: ${bill.entryCount}
⚖️ Total Weight: ${formatWeight(bill.totalWeight)}
💰 Total Amount: ${formatCurrency(bill.totalAmount)}

${entriesDetails}

Generated by Paddy Calculator App`;

      await Share.share({
        message: message,
        title: `Paddy Bill ${bill.generated_bill_no || bill.bill_no}`
      });
    } catch (error) {
      
    }
  };

  const generateBillPDF = async (bill) => {
    try {
      if (!bill.entries || bill.entries.length === 0) {
        
        return;
      }

      
      

      // Import PDF generator
      const { generatePaddyReceiptPDF } = await import('../utils/pdfGenerator');

      // Ensure entries have the correct structure for PDF generation
      const formattedEntries = bill.entries.map(entry => ({
        ...entry,
        billNo: entry.bill_no || entry.billNo || `Entry-${Date.now()}`,
        // Ensure all required fields exist
        grossWeight: entry.gross_weight || entry.grossWeight || 0,
        tractorWeight: entry.tractor_weight || entry.tractorWeight || 0,
        netWeight: entry.net_weight || entry.netWeight || 0,
        adjustedWeight: entry.adjusted_weight || entry.adjustedWeight || 0,
        calculatedPrice: entry.calculated_price || entry.calculatedPrice || 0,
        rewashPercentage: entry.rewash_percentage || entry.rewashPercentage || 0,
        pricePer900: entry.price_per_900 || entry.pricePer900 || 0,
        tractorNo: entry.tractor_no || entry.tractorNo || '',
        tractorId: entry.tractor_id || entry.tractorId || null,
        buyerId: entry.buyer_id || entry.buyerId || bill.buyer_id,
        sellerId: entry.seller_id || entry.sellerId || bill.seller_id,
        // Add yield-related fields for PDF
        yieldRatio: entry.yield_ratio || entry.yieldRatio || 0,
        numBags: entry.num_bags || entry.numBags || 0,
        remainingKg: entry.remaining_kg || entry.remainingKg || 0,
        wasteWeight: entry.waste_weight || entry.wasteWeight || 0
      }));

      

      // Generate PDF with bill entries
      const pdfUri = await generatePaddyReceiptPDF(
        formattedEntries,
        {
          title: `Bill ${bill.generated_bill_no || bill.bill_no}`,
          format: 'A4',
          orientation: 'portrait'
        },
        farmers,
        tractors
      );

      

      // Share the PDF
      await Sharing.shareAsync(pdfUri);

    } catch (error) {
      console.error('PDF Generation Error:', error);
      console.error('Error stack:', error.stack);
      Alert.alert('Error', `Failed to generate PDF: ${error.message}`);
    }
  };

  const saveBillPermanently = async (bill) => {
    try {
      if (!bill.entries || bill.entries.length === 0) {
        
        return;
      }

      const billData = {
        bill_no: bill.bill_no,
        generated_bill_no: bill.generated_bill_no,
        buyer_id: bill.buyer_id,
        seller_id: bill.seller_id,
        total_amount: bill.totalAmount,
        total_weight: bill.totalWeight,
        entry_count: bill.entryCount,
        entries: bill.entries,
        notes: `Saved from session on ${new Date().toLocaleDateString()}`
      };

      const savedBillId = await saveBill(billData);
      

      // You could show a success message here
      // For now, just close the dialog
      setDialogVisible(false);
    } catch (error) {
      
      // You could show an error message here
    }
  };

  return (
    <View style={styles.container}>
      {/* Modern Header */}
      <View style={styles.headerContainer}>
        <View style={styles.headerContent}>
          <View style={styles.headerTop}>
            <IconButton
              icon="arrow-left"
              size={24}
              iconColor={Colors.textLight}
              onPress={() => navigation.goBack()}
              style={styles.backButton}
            />
            <View style={styles.headerTitleContainer}>
              <View style={styles.headerTitleRow}>
                <IconButton
                  icon="clock-outline"
                  size={24}
                  iconColor={Colors.secondary}
                  style={styles.headerIcon}
                />
                <Text style={styles.headerTitle}>Transaction History</Text>
              </View>
              <Text style={styles.headerSubtitle}>View and manage your records</Text>
            </View>
            <IconButton
              icon="refresh"
              size={24}
              iconColor={Colors.textLight}
              onPress={onRefresh}
              style={styles.refreshButton}
            />
          </View>
        </View>
      </View>

      <View style={styles.content}>
        {/* Filter Section */}
        <View style={styles.filterSection}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterRow}>
            {/* Buyer Filter */}
            <Menu
              visible={buyerMenuVisible}
              onDismiss={() => setBuyerMenuVisible(false)}
              anchor={
                <TouchableOpacity
                  style={[styles.filterButton, selectedBuyer && styles.filterButtonActive]}
                  onPress={() => setBuyerMenuVisible(true)}
                >
                  <Text style={[styles.filterButtonText, selectedBuyer && styles.filterButtonTextActive]}>
                    👤 {selectedBuyer ? selectedBuyer.name : 'All Buyers'}
                  </Text>
                </TouchableOpacity>
              }
            >
              <Menu.Item onPress={() => { setSelectedBuyer(null); setBuyerMenuVisible(false); }} title="All Buyers" />
              <Divider />
              {farmers.filter(f => f.type === 'buyer' || f.type === 'both').map(buyer => (
                <Menu.Item
                  key={buyer.id}
                  onPress={() => { setSelectedBuyer(buyer); setBuyerMenuVisible(false); }}
                  title={buyer.name}
                />
              ))}
            </Menu>

            {/* Seller Filter */}
            <Menu
              visible={sellerMenuVisible}
              onDismiss={() => setSellerMenuVisible(false)}
              anchor={
                <TouchableOpacity
                  style={[styles.filterButton, selectedSeller && styles.filterButtonActive]}
                  onPress={() => setSellerMenuVisible(true)}
                >
                  <Text style={[styles.filterButtonText, selectedSeller && styles.filterButtonTextActive]}>
                    🌾 {selectedSeller ? selectedSeller.name : 'All Sellers'}
                  </Text>
                </TouchableOpacity>
              }
            >
              <Menu.Item onPress={() => { setSelectedSeller(null); setSellerMenuVisible(false); }} title="All Sellers" />
              <Divider />
              {farmers.filter(f => f.type === 'seller' || f.type === 'both').map(seller => (
                <Menu.Item
                  key={seller.id}
                  onPress={() => { setSelectedSeller(seller); setSellerMenuVisible(false); }}
                  title={seller.name}
                />
              ))}
            </Menu>

            {/* Tractor Filter */}
            <Menu
              visible={tractorMenuVisible}
              onDismiss={() => setTractorMenuVisible(false)}
              anchor={
                <TouchableOpacity
                  style={[styles.filterButton, selectedTractor && styles.filterButtonActive]}
                  onPress={() => setTractorMenuVisible(true)}
                >
                  <Text style={[styles.filterButtonText, selectedTractor && styles.filterButtonTextActive]}>
                    🚛 {selectedTractor ? selectedTractor.vehicle_number : 'All Tractors'}
                  </Text>
                </TouchableOpacity>
              }
            >
              <Menu.Item onPress={() => { setSelectedTractor(null); setTractorMenuVisible(false); }} title="All Tractors" />
              <Divider />
              {tractors.map(tractor => (
                <Menu.Item
                  key={tractor.id}
                  onPress={() => { setSelectedTractor(tractor); setTractorMenuVisible(false); }}
                  title={`${tractor.vehicle_number} (${tractor.driver_name})`}
                />
              ))}
            </Menu>

            {/* Date Filter */}
            <TouchableOpacity
              style={[styles.filterButton, selectedDate && styles.filterButtonActive]}
              onPress={() => {
                // For now, just toggle today's date
                if (selectedDate) {
                  setSelectedDate(null);
                } else {
                  setSelectedDate(new Date());
                }
              }}
            >
              <Text style={[styles.filterButtonText, selectedDate && styles.filterButtonTextActive]}>
                📅 {selectedDate ? 'Today' : 'All Dates'}
              </Text>
            </TouchableOpacity>
          </ScrollView>
        </View>

        {/* Summary Section */}
        <Card style={styles.summaryCard}>
          <Card.Content>
            <Text style={styles.summaryTitle}>📊 Summary</Text>
            <View style={styles.summaryGrid}>
              <View style={styles.summaryRow}>
                <View style={styles.summaryItem}>
                  <Text style={styles.summaryLabel}>Transactions</Text>
                  <Text style={styles.summaryValue}>{summary.totalEntries}</Text>
                </View>
                <View style={styles.summaryItem}>
                  <Text style={styles.summaryLabel}>Total Amount</Text>
                  <Text style={styles.summaryValue}>{formatCurrency(summary.totalAmount)}</Text>
                </View>
              </View>
              <View style={styles.summaryRow}>
                <View style={styles.summaryItem}>
                  <Text style={styles.summaryLabel}>Total Weight</Text>
                  <Text style={styles.summaryValue}>{formatWeight(summary.totalWeight)}</Text>
                </View>
                <View style={styles.summaryItem}>
                  <Text style={styles.summaryLabel}>Avg Price</Text>
                  <Text style={styles.summaryValue}>{formatCurrency(summary.avgPrice)}</Text>
                </View>
              </View>
            </View>
          </Card.Content>
        </Card>

        <Searchbar
          placeholder="Search transactions..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchbar}
        />

        <ScrollView
          style={styles.scrollView}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          showsVerticalScrollIndicator={false}
        >
          {/* Bill Cards */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>📝 Bills ({entries.length})</Text>
            {entries.length === 0 ? (
              <Card style={styles.emptyCard}>
                <Card.Content>
                  <Text style={styles.emptyText}>No bills found</Text>
                </Card.Content>
              </Card>
            ) : (
              entries.map((bill, index) => (
                <Card key={`bill-${bill.id || Math.random()}-${bill.created_at || Date.now()}-${index}`} style={styles.transactionCard} elevation={2}>
                  <Card.Content>
                    <View style={styles.transactionHeader}>
                      <View style={styles.transactionInfo}>
                        <Text style={styles.transactionTitle}>
                          📋 Bill: {bill.generated_bill_no || bill.bill_no}
                        </Text>
                        {bill.generated_bill_no && bill.entries && bill.entries.length > 0 && (
                          <Text style={styles.receiptNumbers}>
                            📄 Receipts: {bill.entries.map(entry => entry.bill_no).join(', ')}
                          </Text>
                        )}
                        <Text style={styles.transactionDate}>
                          🕒 {formatDate(bill.created_at)}
                        </Text>
                        <Text style={styles.entryCount}>
                          📝 {bill.entryCount} {bill.entryCount === 1 ? 'entry' : 'entries'}
                        </Text>
                      </View>
                      <View style={styles.transactionActions}>
                        <Text style={styles.transactionAmount}>
                          {formatCurrency(bill.totalAmount)}
                        </Text>
                        <View style={styles.actionButtons}>
                          <IconButton
                            icon="eye"
                            size={20}
                            iconColor={Colors.primary}
                            style={styles.actionButton}
                            onPress={() => viewBillDetails(bill)}
                          />
                          <IconButton
                            icon="share"
                            size={20}
                            iconColor={Colors.secondary}
                            style={styles.actionButton}
                            onPress={() => shareBill(bill)}
                          />
                          <IconButton
                            icon="receipt"
                            size={20}
                            iconColor={Colors.accent}
                            style={styles.actionButton}
                            onPress={() => generateBillPDF(bill)}
                          />
                        </View>
                      </View>
                    </View>

                    {/* Buyer, Seller, Tractor Info */}
                    <View style={styles.transactionDetails}>
                      <View style={styles.detailRow}>
                        <Text style={styles.detailLabel}>👤 Buyer:</Text>
                        <Text style={styles.detailValue}>{getBuyerName(bill.buyer_id)}</Text>
                      </View>
                      <View style={styles.detailRow}>
                        <Text style={styles.detailLabel}>🌾 Seller:</Text>
                        <Text style={styles.detailValue}>{getSellerName(bill.seller_id)}</Text>
                      </View>
                      <View style={styles.detailRow}>
                        <Text style={styles.detailLabel}>🚛 Tractor:</Text>
                        <Text style={styles.detailValue}>{getTractorSummary(bill)}</Text>
                      </View>
                    </View>

                    {/* Bill Summary Stats */}
                    <View style={styles.transactionStats}>
                      <Chip icon="receipt" mode="outlined" compact style={styles.statChip}>
                        {bill.entryCount} entries
                      </Chip>
                      <Chip icon="scale-balance" mode="outlined" compact style={styles.statChip}>
                        Total: {formatWeight(bill.totalWeight)}
                      </Chip>
                      <Chip icon="currency-inr" mode="outlined" compact style={styles.statChip}>
                        {formatCurrency(bill.totalAmount)}
                      </Chip>
                    </View>
                  </Card.Content>
                </Card>
              ))
            )}
          </View>
        </ScrollView>
      </View>

      {/* Bill Details Dialog */}
      <Portal>
        <Dialog visible={dialogVisible} onDismiss={() => setDialogVisible(false)}>
          <Dialog.Title style={styles.dialogTitle}>
            📋 Bill Details: {selectedSession?.generated_bill_no || selectedSession?.bill_no}
          </Dialog.Title>
          <Dialog.ScrollArea>
            <ScrollView>
              {selectedSession && (
                <View style={styles.dialogContent}>
                  <View style={styles.dialogHeader}>
                    <Text style={styles.dialogBillNo}>
                      Bill: {selectedSession.bill_no}
                    </Text>
                    <Text style={styles.dialogDate}>
                      🕒 {formatDate(selectedSession.created_at)}
                    </Text>
                  </View>

                  <View style={styles.dialogParties}>
                    <View style={styles.dialogParty}>
                      <Text style={styles.dialogPartyLabel}>👤 Buyer:</Text>
                      <Text style={styles.dialogPartyValue}>{getBuyerName(selectedSession.buyer_id)}</Text>
                    </View>
                    <View style={styles.dialogParty}>
                      <Text style={styles.dialogPartyLabel}>🌾 Seller:</Text>
                      <Text style={styles.dialogPartyValue}>{getSellerName(selectedSession.seller_id)}</Text>
                    </View>
                  </View>

                  <View style={styles.dialogSummary}>
                    <Text style={styles.dialogSummaryTitle}>📊 Summary</Text>
                    <View style={styles.dialogSummaryRow}>
                      <Text style={styles.dialogSummaryLabel}>Total Entries:</Text>
                      <Text style={styles.dialogSummaryValue}>{selectedSession.entryCount}</Text>
                    </View>
                    <View style={styles.dialogSummaryRow}>
                      <Text style={styles.dialogSummaryLabel}>Total Weight:</Text>
                      <Text style={styles.dialogSummaryValue}>{formatWeight(selectedSession.totalWeight)}</Text>
                    </View>
                    <View style={styles.dialogSummaryRow}>
                      <Text style={styles.dialogSummaryLabel}>Total Amount:</Text>
                      <Text style={styles.dialogSummaryValue}>{formatCurrency(selectedSession.totalAmount)}</Text>
                    </View>
                  </View>

                  <Text style={styles.dialogEntriesTitle}>📝 Individual Entries:</Text>
                  {selectedSession.entries?.map((entry, index) => (
                    <View key={`dialog-entry-${entry.id || Math.random()}-${entry.created_at || Date.now()}-${index}`} style={styles.dialogEntry}>
                      <Text style={styles.dialogEntryTitle}>
                        📋 Receipt: {entry.bill_no || `Entry ${index + 1}`}
                      </Text>
                      <View style={styles.dialogEntryDetails}>
                        <Text style={styles.dialogEntryDetail}>
                          🚛 Tractor: {entry.tractor_no}
                        </Text>
                        <Text style={styles.dialogEntryDetail}>
                          ⚖️ Gross: {formatWeight(entry.gross_weight)}
                        </Text>
                        <Text style={styles.dialogEntryDetail}>
                          🚚 Tractor Wt: {formatWeight(entry.tractor_weight)}
                        </Text>
                        <Text style={styles.dialogEntryDetail}>
                          📏 Net: {formatWeight(entry.net_weight)}
                        </Text>
                        <Text style={styles.dialogEntryDetail}>
                          🔄 Rewash: {entry.rewash_percentage}%
                        </Text>
                        <Text style={styles.dialogEntryDetail}>
                          ⚖️ Adjusted: {formatWeight(entry.adjusted_weight)}
                        </Text>
                        <Text style={styles.dialogEntryDetail}>
                          💰 Rate: ₹{entry.price_per_900}/900kg
                        </Text>
                        <Text style={styles.dialogEntryDetail}>
                          💵 Amount: {formatCurrency(entry.calculated_price)}
                        </Text>
                        <Text style={styles.dialogEntryDetail}>
                          📊 Yield: {(() => {
                            const yieldRatio = entry.yield_ratio || 0;
                            const numBags = entry.num_bags || 0;
                            const remainingKg = entry.remaining_kg || 0;
                            const puttiText = yieldRatio === 1 ? 'Putti' : 'Puttla';
                            const bastaText = numBags === 1 ? 'Basta' : 'Bastala';
                            return `${yieldRatio} ${puttiText} ${numBags} ${bastaText} ${remainingKg.toFixed(2)} Kg's`;
                          })()}
                        </Text>
                      </View>
                    </View>
                  ))}

                  {/* Yield Summary Section */}
                  <View style={styles.yieldSummarySection}>
                    <Text style={styles.yieldSummaryTitle}>📊 Yield Summary</Text>
                    {selectedSession.entries?.map((entry, index) => (
                      <Text key={`dialog-yield-${entry.id || Math.random()}-${entry.created_at || Date.now()}-${index}`} style={styles.yieldSummaryEntry}>
                        Entry {index + 1}: {(() => {
                          const yieldRatio = entry.yield_ratio || 0;
                          const numBags = entry.num_bags || 0;
                          const remainingKg = entry.remaining_kg || 0;
                          const puttiText = yieldRatio === 1 ? 'Putti' : 'Puttla';
                          const bastaText = numBags === 1 ? 'Basta' : 'Bastala';
                          return `${yieldRatio} ${puttiText} ${numBags} ${bastaText} ${remainingKg.toFixed(2)} Kg's`;
                        })()}
                      </Text>
                    ))}
                    <Text style={styles.yieldSummaryTotal}>
                      Overall Total: {(() => {
                        const totalAdjusted = selectedSession.entries?.reduce((sum, entry) => sum + (entry.adjusted_weight || 0), 0) || 0;
                        const overallYieldRatio = Math.floor(totalAdjusted / 900);
                        const overallPaddyWeight = totalAdjusted - (overallYieldRatio * 900);
                        const overallBags = Math.floor(overallPaddyWeight / 45);
                        const overallRemainingKg = overallPaddyWeight % 45;
                        const puttiText = overallYieldRatio === 1 ? 'Putti' : 'Puttla';
                        const bastaText = overallBags === 1 ? 'Basta' : 'Bastala';
                        return `${overallYieldRatio} ${puttiText} ${overallBags} ${bastaText} ${overallRemainingKg.toFixed(2)} Kg's`;
                      })()}
                    </Text>
                  </View>
                </View>
              )}
            </ScrollView>
          </Dialog.ScrollArea>
          <Dialog.Actions style={styles.dialogActions}>
            <View style={styles.buttonRow}>
              <Button
                mode="outlined"
                onPress={() => shareBill(selectedSession)}
                style={styles.compactButton}
                icon="share"
                compact
              >
                Share
              </Button>
              <Button
                mode="contained"
                onPress={() => generateBillPDF(selectedSession)}
                style={styles.compactButton}
                icon="file-pdf-box"
                compact
              >
                PDF
              </Button>
            </View>
            <View style={styles.buttonRow}>
              <Button
                mode="contained-tonal"
                onPress={() => saveBillPermanently(selectedSession)}
                style={styles.compactButton}
                icon="content-save"
                compact
              >
                Save Bill
              </Button>
              <Button
                mode="text"
                onPress={() => setDialogVisible(false)}
                style={styles.compactButton}
                compact
              >
                Close
              </Button>
            </View>
          </Dialog.Actions>
        </Dialog>
      </Portal>

      {/* Date Picker */}

      {/* FAB for new calculation */}
      <FAB
        icon="plus"
        style={styles.fab}
        onPress={() => navigation.navigate('Calculator')}
        label="New"
      />

      {/* Enhanced Bottom Navigation */}
      <View style={styles.modernBottomNav}>
        {/* Gradient Top Border */}
        <LinearGradient
          colors={[Colors.primary, Colors.secondary]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.navGradientBorder}
        />

        {/* Moving Tractor Animation */}
        <Animated.View
          style={[
            styles.movingTractor,
            {
              transform: [
                {
                  translateX: tractorAnimation.interpolate({
                    inputRange: [0, 1],
                    outputRange: [-60, 420],
                  }),
                },
              ],
            },
          ]}
        >
          <View style={styles.tractorIcon}>
            <TractorWithTrolley size={28} color={Colors.primary} />
          </View>
        </Animated.View>

        <View style={styles.modernBottomNavContainer}>
          <View style={styles.modernNavButton}>
            <View style={styles.navIconContainer}>
              <IconButton
                icon="calculator"
                iconColor={Colors.primary}
                size={24}
                onPress={() => navigation.navigate('Calculator')}
                style={styles.modernNavIcon}
              />
            </View>
            <Text style={styles.modernNavLabel}>Calculator</Text>
          </View>
          <View style={styles.modernNavButton}>
            <View style={styles.navIconContainer}>
              <IconButton
                icon="account-multiple"
                iconColor={Colors.primary}
                size={24}
                onPress={() => navigation.navigate('FarmerManagement')}
                style={styles.modernNavIcon}
              />
            </View>
            <Text style={styles.modernNavLabel}>Farmers</Text>
          </View>
          <View style={styles.modernNavButton}>
            <View style={styles.navIconContainer}>
              <IconButton
                icon="truck-delivery"
                iconColor={Colors.primary}
                size={24}
                onPress={() => navigation.navigate('TractorManagement')}
                style={styles.modernNavIcon}
              />
            </View>
            <Text style={styles.modernNavLabel}>Tractors</Text>
          </View>
          <View style={[styles.modernNavButton, styles.activeNavButton]}>
            <View style={[styles.navIconContainer, styles.activeNavIconContainer]}>
              <IconButton
                icon="clock-outline"
                iconColor={Colors.secondary}
                size={24}
                onPress={() => navigation.navigate('History')}
                style={styles.modernNavIcon}
              />
            </View>
            <Text style={[styles.modernNavLabel, styles.activeNavLabel]}>History</Text>
          </View>
          <View style={styles.modernNavButton}>
            <View style={styles.navIconContainer}>
              <IconButton
                icon="bookmark-outline"
                iconColor={Colors.primary}
                size={24}
                onPress={() => navigation.navigate('SavedBills')}
                style={styles.modernNavIcon}
              />
            </View>
            <Text style={styles.modernNavLabel}>Saved</Text>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    paddingBottom: 100, // Reduced padding since tractor is on gradient line
  },
  // Modern Header Styles
  headerContainer: {
    backgroundColor: Colors.primary,
    paddingTop: 50,
    paddingBottom: Spacing.lg,
    borderBottomLeftRadius: BorderRadius.xl,
    borderBottomRightRadius: BorderRadius.xl,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  headerContent: {
    paddingHorizontal: Spacing.lg,
  },
  headerTop: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    margin: 0,
  },
  refreshButton: {
    margin: 0,
  },
  headerTitleContainer: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerIcon: {
    margin: 0,
    marginRight: 8,
  },
  headerTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.secondary, // Yellow text on dark green header
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: Typography.fontSize.sm,
    color: Colors.secondary, // Yellow text on dark green header
    opacity: 0.9,
    textAlign: 'center',
    marginTop: 2,
  },

  content: {
    flex: 1,
    paddingTop: Spacing.md,
  },
  searchbar: {
    margin: Spacing.md,
    borderRadius: BorderRadius.lg,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    marginBottom: Spacing.xl,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.primary,
    marginHorizontal: Spacing.md,
    marginBottom: Spacing.md,
  },
  sessionCard: {
    marginHorizontal: Spacing.md,
    marginBottom: Spacing.md,
    borderRadius: BorderRadius.lg,
    backgroundColor: Colors.surface,
  },
  sessionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  sessionInfo: {
    flex: 1,
  },
  sessionName: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.primary,
    marginBottom: Spacing.xs,
  },
  sessionDate: {
    fontSize: Typography.fontSize.sm,
    color: Colors.textSecondary,
  },
  sessionStats: {
    flexDirection: 'row',
    gap: Spacing.sm,
    justifyContent: 'flex-start',
  },
  statChip: {
    backgroundColor: Colors.primaryLight,
  },
  eyeButton: {
    backgroundColor: Colors.primaryLight,
    borderRadius: BorderRadius.full,
  },

  emptyCard: {
    marginHorizontal: Spacing.md,
    borderRadius: BorderRadius.md,
  },
  emptyText: {
    textAlign: 'center',
    color: Colors.textSecondary,
    fontStyle: 'italic',
  },

  // Filter Section Styles
  filterSection: {
    backgroundColor: Colors.surface,
    paddingVertical: Spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  filterRow: {
    paddingHorizontal: Spacing.md,
  },
  filterButton: {
    backgroundColor: Colors.background,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    marginRight: Spacing.sm,
    borderRadius: BorderRadius.md,
    borderWidth: 1,
    borderColor: Colors.border,
    minWidth: 100,
  },
  filterButtonActive: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  filterButtonText: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text,
    textAlign: 'center',
  },
  filterButtonTextActive: {
    color: Colors.surface,
    fontWeight: Typography.fontWeight.medium,
  },

  // Summary Card Styles
  summaryCard: {
    marginHorizontal: Spacing.md,
    marginVertical: Spacing.sm,
    borderRadius: BorderRadius.lg,
  },
  summaryTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.primary,
    marginBottom: Spacing.sm,
  },
  summaryGrid: {
    gap: Spacing.sm,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: Spacing.xs,
  },
  summaryItem: {
    alignItems: 'center',
    flex: 1,
    paddingHorizontal: Spacing.xs,
  },
  summaryLabel: {
    fontSize: Typography.fontSize.xs,
    color: Colors.textSecondary,
    marginBottom: Spacing.xs,
    textAlign: 'center',
  },
  summaryValue: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.primary,
    textAlign: 'center',
    flexWrap: 'wrap',
  },

  // Transaction Card Styles
  transactionCard: {
    marginHorizontal: Spacing.md,
    marginBottom: Spacing.md,
    borderRadius: BorderRadius.lg,
    backgroundColor: Colors.surface,
  },
  transactionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: Spacing.sm,
  },
  transactionInfo: {
    flex: 1,
  },
  transactionTitle: {
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.primary,
    marginBottom: Spacing.xs,
  },
  receiptNumbers: {
    fontSize: Typography.fontSize.xs,
    color: Colors.textSecondary,
    fontStyle: 'italic',
    marginBottom: Spacing.xs,
  },
  transactionDate: {
    fontSize: Typography.fontSize.sm,
    color: Colors.textSecondary,
  },
  entryCount: {
    fontSize: Typography.fontSize.xs,
    color: Colors.primary,
    fontWeight: Typography.fontWeight.medium,
    marginTop: Spacing.xs,
  },
  transactionActions: {
    alignItems: 'flex-end',
  },
  transactionAmount: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.success,
    marginBottom: Spacing.xs,
  },
  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    backgroundColor: Colors.primaryLight,
    borderRadius: BorderRadius.full,
    marginLeft: Spacing.xs,
  },
  transactionDetails: {
    marginBottom: Spacing.sm,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  detailLabel: {
    fontSize: Typography.fontSize.sm,
    color: Colors.textSecondary,
    fontWeight: Typography.fontWeight.medium,
  },
  detailValue: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text,
    fontWeight: Typography.fontWeight.medium,
    flex: 1,
    textAlign: 'right',
  },
  transactionStats: {
    flexDirection: 'row',
    gap: Spacing.xs,
    flexWrap: 'wrap',
  },

  // FAB Style
  fab: {
    position: 'absolute',
    margin: Spacing.md,
    right: 0,
    bottom: 0,
    backgroundColor: Colors.secondary,
  },
  dialogTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.primary,
    textAlign: 'center',
  },
  dialogContent: {
    padding: Spacing.md,
  },
  dialogHeader: {
    marginBottom: Spacing.md,
    paddingBottom: Spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  dialogBillNo: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.primary,
    marginBottom: Spacing.xs,
  },
  dialogDate: {
    fontSize: Typography.fontSize.sm,
    color: Colors.textSecondary,
    fontStyle: 'italic',
  },
  dialogParties: {
    marginBottom: Spacing.md,
    padding: Spacing.md,
    backgroundColor: '#f8f9fa',
    borderRadius: BorderRadius.md,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  dialogParty: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
    paddingVertical: Spacing.xs,
  },
  dialogPartyLabel: {
    fontSize: Typography.fontSize.md,
    color: Colors.textSecondary,
    fontWeight: Typography.fontWeight.semibold,
    minWidth: 80,
  },
  dialogPartyValue: {
    fontSize: Typography.fontSize.md,
    color: Colors.text,
    fontWeight: Typography.fontWeight.medium,
    flex: 1,
    textAlign: 'right',
  },
  dialogSummary: {
    marginBottom: Spacing.md,
    padding: Spacing.md,
    backgroundColor: Colors.primaryLight,
    borderRadius: BorderRadius.md,
    borderWidth: 1,
    borderColor: Colors.primary + '20',
  },
  dialogSummaryTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.primary,
    marginBottom: Spacing.md,
    textAlign: 'center',
  },
  dialogSummaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  dialogSummaryLabel: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text,
    fontWeight: Typography.fontWeight.medium,
  },
  dialogSummaryValue: {
    fontSize: Typography.fontSize.sm,
    color: Colors.primary,
    fontWeight: Typography.fontWeight.bold,
  },
  dialogEntriesTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.primary,
    marginBottom: Spacing.md,
    marginTop: Spacing.md,
    textAlign: 'center',
  },
  dialogEntry: {
    marginBottom: Spacing.md,
    padding: Spacing.md,
    backgroundColor: '#ffffff',
    borderRadius: BorderRadius.md,
    borderWidth: 1,
    borderColor: Colors.border,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  dialogEntryTitle: {
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.primary,
    marginBottom: Spacing.sm,
    paddingBottom: Spacing.xs,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  dialogEntryDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.sm,
  },
  dialogEntryDetail: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text,
    backgroundColor: '#f8f9fa',
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.sm,
    borderWidth: 1,
    borderColor: Colors.border,
    fontWeight: Typography.fontWeight.medium,
  },
  dialogActions: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    flexDirection: 'column',
    gap: Spacing.xs,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: Spacing.sm,
  },
  compactButton: {
    flex: 1,
    marginHorizontal: Spacing.xs,
  },
  dialogButton: {
    marginHorizontal: Spacing.xs,
  },
  yieldSummarySection: {
    marginTop: Spacing.lg,
    padding: Spacing.md,
    backgroundColor: '#f0f8f0',
    borderRadius: BorderRadius.md,
    borderWidth: 1,
    borderColor: '#4CAF50',
  },
  yieldSummaryTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
    color: '#2E7D32',
    marginBottom: Spacing.sm,
    textAlign: 'center',
  },
  yieldSummaryEntry: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text,
    marginBottom: Spacing.xs,
    paddingLeft: Spacing.sm,
  },
  yieldSummaryTotal: {
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.bold,
    color: '#2E7D32',
    marginTop: Spacing.sm,
    paddingTop: Spacing.sm,
    borderTopWidth: 1,
    borderTopColor: '#4CAF50',
    textAlign: 'center',
  },

  // Enhanced Bottom Navigation Styles
  modernBottomNav: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: Colors.surface,
    paddingBottom: 12,
    paddingTop: 15, // Reduced padding
    elevation: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'visible',
  },
  navGradientBorder: {
    height: 3,
    width: '100%',
  },
  movingTractor: {
    position: 'absolute',
    top: -18, // Position tractor on the gradient line
    zIndex: 10,
  },
  tractorIcon: {
    backgroundColor: Colors.secondary,
    borderRadius: 16,
    elevation: 6,
    shadowColor: Colors.primary,
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.4,
    shadowRadius: 6,
    width: 36,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modernBottomNavContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingTop: 12,
    paddingHorizontal: Spacing.xs,
    paddingBottom: 2,
  },
  modernNavButton: {
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 60,
    paddingVertical: 4,
  },
  navIconContainer: {
    backgroundColor: Colors.secondaryLowOpacity,
    borderRadius: 10,
    padding: 2,
    marginBottom: 2,
  },
  activeNavButton: {
    // Active button styling
  },
  activeNavIconContainer: {
    backgroundColor: Colors.primary,
  },
  modernNavIcon: {
    margin: 0,
  },
  modernNavLabel: {
    fontSize: 11,
    color: Colors.primary,
    fontWeight: '600',
    textAlign: 'center',
  },
  activeNavLabel: {
    color: Colors.secondary,
    fontWeight: '700',
  },

});

export default HistoryScreen;
