import React, { useState, useEffect, useCallback, useRef } from 'react';
import { View, StyleSheet, ScrollView, RefreshControl, Animated } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { 
  Appbar, 
  Card, 
  Text, 
  List, 
  Chip, 
  IconButton, 
  Searchbar,
  FAB,
  Portal,
  Modal,
  Button,
  Snackbar
} from 'react-native-paper';
import { useFocusEffect } from '@react-navigation/native';
import TractorWithTrolley from '../components/TractorWithTrolley';
import { Colors, Spacing, Typography, BorderRadius } from '../constants/theme';
import { getAllFarmers, insertFarmer, updateFarmer, deleteFarmer } from '../database/database';
import FarmerForm from '../components/FarmerForm';

const FarmerManagementScreen = ({ navigation }) => {
  const [farmers, setFarmers] = useState([]);
  const [filteredFarmers, setFilteredFarmers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [modalVisible, setModalVisible] = useState(false);
  const [editingFarmer, setEditingFarmer] = useState(null);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  // Tractor Animation
  const tractorPosition = useRef(new Animated.Value(0)).current;

  // Load data when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      loadFarmers();
    }, [])
  );

  // Tractor Animation Effect
  useEffect(() => {
    const animateTractor = () => {
      Animated.loop(
        Animated.sequence([
          Animated.timing(tractorPosition, {
            toValue: 300,
            duration: 8000,
            useNativeDriver: true,
          }),
          Animated.timing(tractorPosition, {
            toValue: 0,
            duration: 0,
            useNativeDriver: true,
          }),
        ])
      ).start();
    };

    animateTractor();
  }, [tractorPosition]);

  // Filter farmers when search query changes
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredFarmers(farmers);
    } else {
      const filtered = farmers.filter(farmer =>
        farmer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        farmer.phone?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        farmer.village?.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredFarmers(filtered);
    }
  }, [farmers, searchQuery]);

  const loadFarmers = async () => {
    try {
      setLoading(true);
      const farmersData = await getAllFarmers();
      setFarmers(farmersData);
    } catch (error) {
      
      showSnackbar('Error loading farmers');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadFarmers();
    setRefreshing(false);
  };

  const handleAddFarmer = () => {
    setEditingFarmer(null);
    setModalVisible(true);
  };

  const handleEditFarmer = (farmer) => {
    setEditingFarmer(farmer);
    setModalVisible(true);
  };

  const handleDeleteFarmer = async (farmer) => {
    try {
      await deleteFarmer(farmer.id);
      showSnackbar(`${farmer.name} deleted successfully`);
      loadFarmers();
    } catch (error) {
      
      showSnackbar('Error deleting farmer');
    }
  };

  const handleFormSubmit = async (formData) => {
    try {
      if (editingFarmer) {
        await updateFarmer(editingFarmer.id, formData);
        showSnackbar('Farmer updated successfully');
      } else {
        await insertFarmer(formData);
        showSnackbar('Farmer added successfully');
      }
      setModalVisible(false);
      loadFarmers();
    } catch (error) {
      
      showSnackbar('Error saving farmer');
    }
  };

  const handleFormCancel = () => {
    setModalVisible(false);
    setEditingFarmer(null);
  };

  const showSnackbar = (message) => {
    setSnackbarMessage(message);
    setSnackbarVisible(true);
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'buyer': return '🛒';
      case 'seller': return '🌾';
      case 'both': return '🔄';
      default: return '👤';
    }
  };

  const getTypeColor = (type) => {
    switch (type) {
      case 'buyer': return Colors.info;
      case 'seller': return Colors.success;
      case 'both': return Colors.secondary;
      default: return Colors.textSecondary;
    }
  };

  return (
    <View style={styles.container}>
      {/* Modern Header */}
      <View style={styles.headerContainer}>
        <View style={styles.headerContent}>
          <View style={styles.headerTop}>
            <IconButton
              icon="arrow-left"
              size={24}
              iconColor={Colors.textLight}
              onPress={() => navigation.goBack()}
              style={styles.backButton}
            />
            <View style={styles.headerTitleContainer}>
              <View style={styles.headerTitleRow}>
                <IconButton
                  icon="account-multiple"
                  size={24}
                  iconColor={Colors.secondary}
                  style={styles.headerIcon}
                />
                <Text style={styles.headerTitle}>Farmer Management</Text>
              </View>
              <Text style={styles.headerSubtitle}>Manage buyers and sellers</Text>
            </View>
            <IconButton
              icon="refresh"
              size={24}
              iconColor={Colors.textLight}
              onPress={onRefresh}
              style={styles.refreshButton}
            />
          </View>
        </View>
      </View>

      <View style={styles.content}>
        {/* Stats Cards */}
        <View style={styles.statsContainer}>
          <View style={styles.statsCard}>
            <Text style={styles.statsNumber}>{farmers.length}</Text>
            <Text style={styles.statsLabel}>Total Farmers</Text>
          </View>
          <View style={styles.statsCard}>
            <Text style={styles.statsNumber}>{farmers.filter(f => f.type === 'buyer').length}</Text>
            <Text style={styles.statsLabel}>Buyers</Text>
          </View>
          <View style={styles.statsCard}>
            <Text style={styles.statsNumber}>{farmers.filter(f => f.type === 'seller').length}</Text>
            <Text style={styles.statsLabel}>Sellers</Text>
          </View>
        </View>

        {/* Enhanced Search Bar */}
        <Searchbar
          placeholder="Search by name, phone, or village..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
          iconColor={Colors.primary}
          inputStyle={styles.searchInput}
        />

        <ScrollView
          style={styles.scrollView}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          showsVerticalScrollIndicator={false}
        >
          {filteredFarmers.length === 0 ? (
            <Card style={styles.emptyCard}>
              <Card.Content>
                <Text style={styles.emptyText}>
                  {searchQuery ? 'No farmers found matching your search' : 'No farmers added yet'}
                </Text>
                <Text style={styles.emptySubtext}>
                  {searchQuery ? 'Try a different search term' : 'Add your first farmer using the + button'}
                </Text>
              </Card.Content>
            </Card>
          ) : (
            filteredFarmers.map((farmer) => (
              <Card key={farmer.id} style={styles.farmerCard} elevation={2}>
                <Card.Content>
                  <View style={styles.farmerHeader}>
                    <View style={styles.farmerInfo}>
                      <View style={styles.farmerTitleRow}>
                        <Text style={styles.farmerName}>{getTypeIcon(farmer.type)} {farmer.name}</Text>
                        <View style={styles.farmerActions}>
                          <IconButton
                            icon="pencil"
                            size={20}
                            iconColor={Colors.primary}
                            onPress={() => handleEditFarmer(farmer)}
                            style={styles.actionButton}
                          />
                          <IconButton
                            icon="delete"
                            iconColor={Colors.error}
                            size={20}
                            onPress={() => handleDeleteFarmer(farmer)}
                            style={styles.actionButton}
                          />
                        </View>
                      </View>

                      <View style={styles.farmerTypeCard}>
                        <Text style={[styles.farmerType, { color: getTypeColor(farmer.type) }]}>
                          {farmer.type.charAt(0).toUpperCase() + farmer.type.slice(1)}
                        </Text>
                      </View>

                      <View style={styles.farmerDetails}>
                        {farmer.phone && (
                          <View style={styles.detailRow}>
                            <Text style={styles.detailIcon}>📞</Text>
                            <Text style={styles.detailText}>{farmer.phone}</Text>
                          </View>
                        )}
                        {farmer.village && (
                          <View style={styles.detailRow}>
                            <Text style={styles.detailIcon}>📍</Text>
                            <Text style={styles.detailText}>{farmer.village}</Text>
                          </View>
                        )}
                      </View>
                    </View>
                  </View>
                </Card.Content>
              </Card>
            ))
          )}
        </ScrollView>
      </View>

      {/* Modern Floating Action Button */}
      <FAB
        icon="plus"
        style={styles.fab}
        onPress={() => navigation.navigate('AddFarmer')}
        label="Add Farmer"
        color={Colors.primary} // Green text on yellow FAB
      />

      {/* Farmer Form Modal */}
      <Portal>
        <Modal
          visible={modalVisible}
          onDismiss={handleFormCancel}
          contentContainerStyle={styles.modalContainer}
        >
          <FarmerForm
            title={editingFarmer ? "Edit Farmer" : "Add Farmer"}
            initialData={editingFarmer}
            onSubmit={handleFormSubmit}
            onCancel={handleFormCancel}
          />
        </Modal>
      </Portal>

      {/* Snackbar */}
      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
        style={styles.snackbar}
        theme={{ colors: { onSurface: Colors.secondary } }}
      >
        {snackbarMessage}
      </Snackbar>

      {/* Enhanced Bottom Navigation */}
      <View style={styles.modernBottomNav}>
        <LinearGradient
          colors={[Colors.primary, Colors.secondary]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.navGradientBorder}
        />
        <Animated.View style={[styles.movingTractor, { transform: [{ translateX: tractorPosition }] }]}>
          <View style={styles.tractorIcon}>
            <TractorWithTrolley size={28} color={Colors.primary} />
          </View>
        </Animated.View>
        <View style={styles.modernBottomNavContainer}>
          <View style={styles.modernNavButton}>
            <View style={styles.navIconContainer}>
              <IconButton
                icon="calculator"
                iconColor={Colors.primary}
                size={24}
                onPress={() => navigation.navigate('Calculator')}
                style={styles.modernNavIcon}
              />
            </View>
            <Text style={styles.modernNavLabel}>Calculator</Text>
          </View>
          <View style={[styles.modernNavButton, styles.activeNavButton]}>
            <View style={[styles.navIconContainer, styles.activeNavIconContainer]}>
              <IconButton
                icon="account-multiple"
                iconColor={Colors.secondary}
                size={24}
                onPress={() => navigation.navigate('FarmerManagement')}
                style={styles.modernNavIcon}
              />
            </View>
            <Text style={[styles.modernNavLabel, styles.activeNavLabel]}>Farmers</Text>
          </View>
          <View style={styles.modernNavButton}>
            <View style={styles.navIconContainer}>
              <IconButton
                icon="truck-delivery"
                iconColor={Colors.primary}
                size={24}
                onPress={() => navigation.navigate('TractorManagement')}
                style={styles.modernNavIcon}
              />
            </View>
            <Text style={styles.modernNavLabel}>Tractors</Text>
          </View>
          <View style={styles.modernNavButton}>
            <View style={styles.navIconContainer}>
              <IconButton
                icon="clock-outline"
                iconColor={Colors.primary}
                size={24}
                onPress={() => navigation.navigate('History')}
                style={styles.modernNavIcon}
              />
            </View>
            <Text style={styles.modernNavLabel}>History</Text>
          </View>
          <View style={styles.modernNavButton}>
            <View style={styles.navIconContainer}>
              <IconButton
                icon="bookmark-outline"
                iconColor={Colors.primary}
                size={24}
                onPress={() => navigation.navigate('SavedBills')}
                style={styles.modernNavIcon}
              />
            </View>
            <Text style={styles.modernNavLabel}>Saved</Text>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingBottom: 100, // Reduced padding since tractor is on gradient line
  },
  // Modern Header Styles
  headerContainer: {
    backgroundColor: Colors.primary,
    paddingTop: 50,
    paddingBottom: Spacing.lg,
    borderBottomLeftRadius: BorderRadius.xl,
    borderBottomRightRadius: BorderRadius.xl,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  headerContent: {
    paddingHorizontal: Spacing.lg,
  },
  headerTop: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    margin: 0,
  },
  refreshButton: {
    margin: 0,
  },
  headerTitleContainer: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerIcon: {
    margin: 0,
    marginRight: 8,
  },
  headerTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.secondary, // Yellow text on dark green header
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: Typography.fontSize.sm,
    color: Colors.secondary, // Yellow text on dark green header
    opacity: 0.9,
    textAlign: 'center',
    marginTop: 2,
  },
  content: {
    flex: 1,
    paddingTop: Spacing.lg,
  },
  // Stats Cards
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
    gap: Spacing.sm,
  },
  statsCard: {
    flex: 1,
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.xl,
    padding: Spacing.lg,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.primaryLowOpacity,
    shadowColor: Colors.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 4,
  },
  statsNumber: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.primary,
  },
  statsLabel: {
    fontSize: Typography.fontSize.xs,
    color: Colors.textSecondary,
    marginTop: Spacing.xs,
    textAlign: 'center',
  },
  // Search Bar
  searchBar: {
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
    borderRadius: BorderRadius.lg,
    elevation: 2,
  },
  searchInput: {
    fontSize: Typography.fontSize.md,
  },
  scrollView: {
    flex: 1,
  },
  // Farmer Cards
  farmerCard: {
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.md,
    borderRadius: BorderRadius.xl,
    backgroundColor: Colors.surface,
    borderWidth: 1,
    borderColor: Colors.primaryLowOpacity,
    shadowColor: Colors.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 4,
  },
  farmerHeader: {
    marginBottom: Spacing.sm,
  },
  farmerInfo: {
    flex: 1,
  },
  farmerTitleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  farmerName: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.primary,
  },
  farmerActions: {
    flexDirection: 'row',
  },
  actionButton: {
    margin: 0,
  },
  farmerTypeCard: {
    alignSelf: 'flex-start',
    backgroundColor: '#f8f9fa',
    borderRadius: BorderRadius.md,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.xs,
    marginBottom: Spacing.md,
  },
  farmerType: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.bold,
  },
  farmerDetails: {
    gap: Spacing.sm,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  detailIcon: {
    fontSize: 16,
    marginRight: Spacing.sm,
  },
  detailText: {
    fontSize: Typography.fontSize.md,
    color: Colors.text,
    fontWeight: Typography.fontWeight.medium,
  },
  emptyCard: {
    marginHorizontal: Spacing.lg,
    borderRadius: BorderRadius.xl,
    backgroundColor: Colors.surface,
    borderWidth: 1,
    borderColor: Colors.primaryLowOpacity,
  },
  emptyText: {
    textAlign: 'center',
    color: Colors.textSecondary,
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.medium,
    marginBottom: Spacing.xs,
  },
  emptySubtext: {
    textAlign: 'center',
    color: Colors.textSecondary,
    fontSize: Typography.fontSize.md,
  },
  fab: {
    position: 'absolute',
    right: Spacing.lg,
    bottom: 120, // Properly positioned above navigation bar
    backgroundColor: Colors.secondary,
    borderRadius: 28,
    elevation: 6,
    shadowColor: Colors.secondary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  snackbar: {
    backgroundColor: Colors.primary,
    marginBottom: 80, // Position above bottom navigation
    elevation: 10, // Higher than bottom navigation
  },
  // Enhanced Bottom Navigation Styles
  modernBottomNav: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: Colors.surface,
    paddingBottom: 12,
    paddingTop: 15, // Reduced padding
    elevation: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'visible',
  },
  navGradientBorder: {
    height: 3,
    width: '100%',
  },
  movingTractor: {
    position: 'absolute',
    top: -18, // Position tractor on the gradient line
    zIndex: 10,
  },
  tractorIcon: {
    backgroundColor: Colors.secondary,
    borderRadius: 16,
    elevation: 6,
    shadowColor: Colors.primary,
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.4,
    shadowRadius: 6,
    width: 36,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modernBottomNavContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingTop: 12,
    paddingHorizontal: Spacing.xs,
    paddingBottom: 2,
  },
  modernNavButton: {
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 60,
    paddingVertical: 4,
  },
  navIconContainer: {
    backgroundColor: Colors.secondaryLowOpacity,
    borderRadius: 10,
    padding: 2,
    marginBottom: 2,
  },
  activeNavButton: {
    // Active button styling
  },
  activeNavIconContainer: {
    backgroundColor: Colors.primary,
  },
  modernNavIcon: {
    margin: 0,
  },
  modernNavLabel: {
    fontSize: 11,
    color: Colors.primary,
    fontWeight: '600',
    textAlign: 'center',
  },
  activeNavLabel: {
    color: Colors.secondary,
    fontWeight: '700',
  },
});

export default FarmerManagementScreen;
