import React, { useState, useEffect, useRef } from 'react';
import { View, StyleSheet, ScrollView, Alert, Animated } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  Appbar,
  Card,
  Text,
  TextInput,
  Button,
  Switch,
  List,
  Divider,
  Snackbar,
  IconButton
} from 'react-native-paper';
import { Colors, Spacing, Typography, BorderRadius } from '../constants/theme';
import TractorWithTrolley from '../components/TractorWithTrolley';
import ModernConfirmDialog from '../components/ModernConfirmDialog';
import { getSetting, setSetting } from '../database/database';

const SettingsScreen = ({ navigation }) => {
  const [settings, setSettings] = useState({
    defaultRewashPercentage: '2.0',
  });

  const [loading, setLoading] = useState(true);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [resetDialogVisible, setResetDialogVisible] = useState(false);

  // Tractor Animation
  const tractorPosition = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    loadSettings();
  }, []);

  // Tractor Animation Effect
  useEffect(() => {
    const animateTractor = () => {
      Animated.loop(
        Animated.sequence([
          Animated.timing(tractorPosition, {
            toValue: 300,
            duration: 8000,
            useNativeDriver: true,
          }),
          Animated.timing(tractorPosition, {
            toValue: 0,
            duration: 0,
            useNativeDriver: true,
          }),
        ])
      ).start();
    };

    animateTractor();
  }, [tractorPosition]);

  const loadSettings = async () => {
    try {
      setLoading(true);
      const loadedSettings = {};
      
      // Load rewash percentage setting
      const defaultRewash = await getSetting('default_rewash_percentage');
      if (defaultRewash) {
        loadedSettings.defaultRewashPercentage = defaultRewash;
      }

      setSettings(prev => ({ ...prev, ...loadedSettings }));
    } catch (error) {
      
      showSnackbar('Error loading settings');
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async () => {
    try {
      // Validate settings
      if (!settings.defaultRewashPercentage || isNaN(settings.defaultRewashPercentage)) {
        showSnackbar('Please enter a valid rewash percentage');
        return;
      }

      // Save settings to database
      await setSetting('default_rewash_percentage', settings.defaultRewashPercentage);

      showSnackbar('Settings saved successfully!');
    } catch (error) {
      
      showSnackbar('Error saving settings');
    }
  };

  const resetToDefaults = () => {
    setResetDialogVisible(true);
  };

  const handleResetConfirm = () => {
    setSettings({
      defaultRewashPercentage: '2.0',
    });
    showSnackbar('Settings reset to defaults');
  };

  const handleSettingChange = (key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const showSnackbar = (message) => {
    setSnackbarMessage(message);
    setSnackbarVisible(true);
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <Appbar.Header>
          <Appbar.BackAction onPress={() => navigation.goBack()} />
          <Appbar.Content title="⚙️ Settings" />
        </Appbar.Header>
        <View style={styles.loadingContainer}>
          <Text>Loading settings...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Modern Header */}
      <View style={styles.headerContainer}>
        <View style={styles.headerContent}>
          <View style={styles.headerTop}>
            <IconButton
              icon="arrow-left"
              size={24}
              iconColor={Colors.textLight}
              onPress={() => navigation.goBack()}
              style={styles.backButton}
            />
            <View style={styles.headerTitleContainer}>
              <View style={styles.headerTitleRow}>
                <IconButton
                  icon="cog"
                  size={24}
                  iconColor={Colors.secondary}
                  style={styles.headerIcon}
                />
                <Text style={styles.headerTitle}>Settings</Text>
              </View>
              <Text style={styles.headerSubtitle}>Configure your preferences</Text>
            </View>
            <View style={styles.headerActions}>
              <IconButton
                icon="refresh"
                size={20}
                iconColor={Colors.textLight}
                onPress={loadSettings}
                style={styles.headerActionButton}
              />
              <IconButton
                icon="content-save"
                size={20}
                iconColor={Colors.textLight}
                onPress={saveSettings}
                style={styles.headerActionButton}
              />
            </View>
          </View>
        </View>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Calculation Settings */}
        <Card style={styles.settingsCard} elevation={3}>
          <Card.Content>
            <View style={styles.cardHeader}>
              <Text style={styles.cardTitle}>📊 Calculation Settings</Text>
              <Text style={styles.cardSubtitle}>Configure default calculation values</Text>
            </View>
            <TextInput
              label="Default Rewash Percentage (%)"
              value={settings.defaultRewashPercentage}
              onChangeText={(value) => handleSettingChange('defaultRewashPercentage', value)}
              keyboardType="numeric"
              mode="outlined"
              style={styles.input}
              theme={{ colors: { primary: Colors.primary } }}
              contentStyle={styles.inputContent}
            />
            <Text style={styles.helperText}>Default rewash percentage for new entries</Text>
          </Card.Content>
        </Card>

        {/* Company Information */}
        <Card style={styles.settingsCard} elevation={3}>
          <Card.Content>
            <View style={styles.cardHeader}>
              <Text style={styles.cardTitle}>🏢 Company Information</Text>
              <Text style={styles.cardSubtitle}>Business details for receipts</Text>
            </View>

            <View style={styles.companyInfoContainer}>
              <View style={styles.companyInfoRow}>
                <Text style={styles.companyInfoLabel}>Company Name:</Text>
                <Text style={styles.companyInfoValue}>CHEVURU SOFTWARE SOLUTIONS</Text>
              </View>

              <View style={styles.companyInfoRow}>
                <Text style={styles.companyInfoLabel}>Address:</Text>
                <Text style={styles.companyInfoValue}>West Street, Kagithalapuru, Pincode 524405</Text>
              </View>

              <View style={styles.companyInfoRow}>
                <Text style={styles.companyInfoLabel}>Phone:</Text>
                <Text style={styles.companyInfoValue}>+91 9502953463</Text>
              </View>

              <View style={styles.companyInfoRow}>
                <Text style={styles.companyInfoLabel}>Email:</Text>
                <Text style={styles.companyInfoValue}><EMAIL></Text>
              </View>

              <View style={styles.companyInfoRow}>
                <Text style={styles.companyInfoLabel}>Website:</Text>
                <Text style={styles.companyInfoValue}>www.chevurusoftwaresolutions.com</Text>
              </View>

              <View style={styles.companyInfoFooter}>
                <Text style={styles.companyInfoFooterText}>
                  This information will appear on all generated PDF receipts and bills.
                </Text>
              </View>
            </View>
          </Card.Content>
        </Card>

        {/* App Information */}
        <Card style={styles.settingsCard} elevation={3}>
          <Card.Content>
            <View style={styles.cardHeader}>
              <Text style={styles.cardTitle}>📱 App Information</Text>
              <Text style={styles.cardSubtitle}>About this application</Text>
            </View>

            <View style={styles.infoSection}>
              <View style={styles.infoItem}>
                <Text style={styles.infoIcon}>ℹ️</Text>
                <View style={styles.infoContent}>
                  <Text style={styles.infoTitle}>Version</Text>
                  <Text style={styles.infoDescription}>1.0.0</Text>
                </View>
              </View>

              <View style={styles.infoItem}>
                <Text style={styles.infoIcon}>👨‍💻</Text>
                <View style={styles.infoContent}>
                  <Text style={styles.infoTitle}>Developer</Text>
                  <Text style={styles.infoDescription}>Sumanth Chevuru</Text>
                </View>
              </View>

              <View style={styles.infoItem}>
                <Text style={styles.infoIcon}>⚛️</Text>
                <View style={styles.infoContent}>
                  <Text style={styles.infoTitle}>Built with</Text>
                  <Text style={styles.infoDescription}>React Native & Expo</Text>
                </View>
              </View>
            </View>
          </Card.Content>
        </Card>

        {/* Action Buttons */}
        <View style={styles.actionContainer}>
          <Button
            mode="contained"
            icon="content-save"
            onPress={saveSettings}
            style={styles.saveButton}
            contentStyle={styles.buttonContent}
            labelStyle={styles.buttonLabel}
          >
            Save Settings
          </Button>

          <Button
            mode="outlined"
            icon="restore"
            onPress={resetToDefaults}
            style={styles.resetButton}
            contentStyle={styles.buttonContent}
            labelStyle={styles.resetButtonLabel}
          >
            Reset to Defaults
          </Button>
        </View>

        {/* Enhanced Footer */}
        <View style={styles.footer}>
          <View style={styles.footerCard}>
            <Text style={styles.footerTitle}>
              🌾 Paddy Calculator
            </Text>
            <Text style={styles.footerSubtitle}>
              Professional paddy purchase management
            </Text>
            <Text style={styles.footerCredit}>
              Developed with ❤️ for farmers and traders
            </Text>
          </View>
        </View>
      </ScrollView>

      {/* Snackbar */}
      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
        style={styles.snackbar}
      >
        {snackbarMessage}
      </Snackbar>

      {/* Enhanced Bottom Navigation */}
      <View style={styles.modernBottomNav}>
        <LinearGradient
          colors={[Colors.primary, Colors.secondary]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.navGradientBorder}
        />
        <Animated.View style={[styles.movingTractor, { transform: [{ translateX: tractorPosition }] }]}>
          <View style={styles.tractorIcon}>
            <TractorWithTrolley size={28} color={Colors.primary} />
          </View>
        </Animated.View>
        <View style={styles.modernBottomNavContainer}>
          <View style={styles.modernNavButton}>
            <View style={styles.navIconContainer}>
              <IconButton
                icon="calculator"
                iconColor={Colors.primary}
                size={24}
                onPress={() => navigation.navigate('Calculator')}
                style={styles.modernNavIcon}
              />
            </View>
            <Text style={styles.modernNavLabel}>Calculator</Text>
          </View>
          <View style={styles.modernNavButton}>
            <View style={styles.navIconContainer}>
              <IconButton
                icon="account-multiple"
                iconColor={Colors.primary}
                size={24}
                onPress={() => navigation.navigate('FarmerManagement')}
                style={styles.modernNavIcon}
              />
            </View>
            <Text style={styles.modernNavLabel}>Farmers</Text>
          </View>
          <View style={styles.modernNavButton}>
            <View style={styles.navIconContainer}>
              <IconButton
                icon="truck-delivery"
                iconColor={Colors.primary}
                size={24}
                onPress={() => navigation.navigate('TractorManagement')}
                style={styles.modernNavIcon}
              />
            </View>
            <Text style={styles.modernNavLabel}>Tractors</Text>
          </View>
          <View style={styles.modernNavButton}>
            <View style={styles.navIconContainer}>
              <IconButton
                icon="clock-outline"
                iconColor={Colors.primary}
                size={24}
                onPress={() => navigation.navigate('History')}
                style={styles.modernNavIcon}
              />
            </View>
            <Text style={styles.modernNavLabel}>History</Text>
          </View>
          <View style={styles.modernNavButton}>
            <View style={styles.navIconContainer}>
              <IconButton
                icon="bookmark-outline"
                iconColor={Colors.primary}
                size={24}
                onPress={() => navigation.navigate('SavedBills')}
                style={styles.modernNavIcon}
              />
            </View>
            <Text style={styles.modernNavLabel}>Saved</Text>
          </View>
        </View>
      </View>

      {/* Modern Reset Confirmation Dialog */}
      <ModernConfirmDialog
        visible={resetDialogVisible}
        onDismiss={() => setResetDialogVisible(false)}
        title="Reset Settings"
        message="Are you sure you want to reset all settings to default values?"
        icon="restore"
        confirmText="Reset"
        cancelText="Cancel"
        onConfirm={handleResetConfirm}
        isDestructive={true}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    paddingBottom: 100, // Space for navigation bar
  },
  // Modern Header Styles
  headerContainer: {
    backgroundColor: Colors.primary,
    paddingTop: 50,
    paddingBottom: Spacing.lg,
    borderBottomLeftRadius: BorderRadius.xl,
    borderBottomRightRadius: BorderRadius.xl,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  headerContent: {
    paddingHorizontal: Spacing.lg,
  },
  headerTop: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    margin: 0,
  },
  headerActions: {
    flexDirection: 'row',
  },
  headerActionButton: {
    margin: 0,
    marginLeft: Spacing.xs,
  },
  headerTitleContainer: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerIcon: {
    margin: 0,
    marginRight: 8,
  },
  headerTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.secondary, // Yellow text like TractorManagement
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: Typography.fontSize.sm,
    color: Colors.secondary, // Yellow text like TractorManagement
    opacity: 0.9,
    textAlign: 'center',
    marginTop: 2,
  },
  scrollView: {
    flex: 1,
    paddingTop: Spacing.lg,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  // Settings Cards
  settingsCard: {
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
    borderRadius: BorderRadius.xl,
    backgroundColor: Colors.surface,
    borderWidth: 1,
    borderColor: Colors.primaryLowOpacity,
    shadowColor: Colors.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
  },
  cardHeader: {
    marginBottom: Spacing.lg,
  },
  cardTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.primary,
    marginBottom: Spacing.xs,
  },
  cardSubtitle: {
    fontSize: Typography.fontSize.sm,
    color: Colors.textSecondary,
  },
  inputGroup: {
    gap: Spacing.md,
  },
  input: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.md,
    borderWidth: 1,
    borderColor: Colors.secondaryLowOpacity,
    marginBottom: Spacing.sm,
  },
  inputContent: {
    fontSize: Typography.fontSize.md,
  },
  helperText: {
    fontSize: Typography.fontSize.sm,
    color: Colors.textSecondary,
    marginTop: Spacing.xs,
    fontStyle: 'italic',
  },
  // Info Section
  infoSection: {
    gap: Spacing.md,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.md,
    backgroundColor: '#f8f9fa',
    borderRadius: BorderRadius.md,
  },
  infoIcon: {
    fontSize: 20,
    marginRight: Spacing.md,
  },
  infoContent: {
    flex: 1,
  },
  infoTitle: {
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.text,
    marginBottom: Spacing.xs,
  },
  infoDescription: {
    fontSize: Typography.fontSize.sm,
    color: Colors.textSecondary,
  },
  // Action Buttons
  actionContainer: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.xl,
    gap: Spacing.md,
  },
  saveButton: {
    backgroundColor: Colors.primary,
    borderRadius: BorderRadius.lg,
  },
  resetButton: {
    borderColor: Colors.error,
    borderRadius: BorderRadius.lg,
  },
  buttonContent: {
    paddingVertical: Spacing.sm,
  },
  buttonLabel: {
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.bold,
  },
  resetButtonLabel: {
    color: Colors.error,
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.bold,
  },
  // Footer
  footer: {
    paddingHorizontal: Spacing.lg,
    paddingBottom: Spacing.xl,
  },
  footerCard: {
    backgroundColor: '#ffffff',
    borderRadius: BorderRadius.lg,
    padding: Spacing.xl,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  footerTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.primary,
    textAlign: 'center',
    marginBottom: Spacing.xs,
  },
  footerSubtitle: {
    fontSize: Typography.fontSize.md,
    color: Colors.text,
    textAlign: 'center',
    marginBottom: Spacing.sm,
  },
  footerCredit: {
    fontSize: Typography.fontSize.sm,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
  snackbar: {
    backgroundColor: Colors.primary,
    marginBottom: 100, // Position above bottom navigation
    elevation: 10, // Higher than bottom navigation
  },

  // Enhanced Bottom Navigation Styles
  modernBottomNav: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: Colors.surface,
    paddingBottom: 12,
    paddingTop: 15, // Reduced padding
    elevation: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'visible',
  },
  navGradientBorder: {
    height: 3,
    width: '100%',
  },
  movingTractor: {
    position: 'absolute',
    top: -18, // Position tractor on the gradient line
    zIndex: 10,
  },
  tractorIcon: {
    backgroundColor: Colors.secondary,
    borderRadius: 16,
    elevation: 6,
    shadowColor: Colors.primary,
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.4,
    shadowRadius: 6,
    width: 36,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modernBottomNavContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingTop: 12,
    paddingHorizontal: Spacing.xs,
    paddingBottom: 2,
  },
  modernNavButton: {
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 60,
    paddingVertical: 4,
  },
  navIconContainer: {
    backgroundColor: Colors.secondaryLowOpacity,
    borderRadius: 10,
    padding: 2,
    marginBottom: 2,
  },
  modernNavIcon: {
    margin: 0,
  },
  modernNavLabel: {
    fontSize: 11,
    color: Colors.primary,
    fontWeight: '600',
    textAlign: 'center',
  },
  // Company Information Styles
  companyInfoContainer: {
    gap: Spacing.md,
  },
  companyInfoRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingVertical: Spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  companyInfoLabel: {
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.primary,
    width: 90,
    marginRight: Spacing.md,
  },
  companyInfoValue: {
    fontSize: Typography.fontSize.md,
    color: Colors.text,
    flex: 1,
    lineHeight: 22,
  },
  companyInfoFooter: {
    marginTop: Spacing.lg,
    padding: Spacing.md,
    backgroundColor: '#e8f5e8',
    borderRadius: BorderRadius.md,
    borderLeftWidth: 4,
    borderLeftColor: Colors.primary,
  },
  companyInfoFooterText: {
    fontSize: Typography.fontSize.sm,
    color: Colors.textSecondary,
    fontStyle: 'italic',
    lineHeight: 20,
    textAlign: 'center',
  },
});

export default SettingsScreen;
