import React, { useState } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import {
  TextInput,
  Button,
  Card,
  Text
} from 'react-native-paper';
import { Colors, Spacing, BorderRadius, Typography } from '../constants/theme';

const TractorForm = ({ 
  onSubmit, 
  onCancel, 
  initialData = {},
  title = "Add Tractor"
}) => {
  const [formData, setFormData] = useState({
    vehicleNumber: (initialData && initialData.vehicle_number) || '',
    brand: (initialData && initialData.brand) || '',
    model: (initialData && initialData.model) || '',
    capacityTons: (initialData && initialData.capacity_tons?.toString()) || '',
    tractorWeightKg: (initialData && initialData.tractor_weight_kg?.toString()) || '',
    driverName: (initialData && initialData.driver_name) || '',
    driverPhone: (initialData && initialData.driver_phone) || '',
    driverLicense: (initialData && initialData.driver_license) || '',
    ownerName: (initialData && initialData.owner_name) || '',
    ownerPhone: (initialData && initialData.owner_phone) || '',
    ownerAddress: (initialData && initialData.owner_address) || '',
    registrationDate: (initialData && initialData.registration_date) || '',
    insuranceExpiry: (initialData && initialData.insurance_expiry) || '',
    fitnessExpiry: (initialData && initialData.fitness_expiry) || '',
    notes: (initialData && initialData.notes) || '',
  });

  const [errors, setErrors] = useState({});

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.vehicleNumber.trim()) {
      newErrors.vehicleNumber = 'Vehicle number is required';
    }

    if (!formData.tractorWeightKg.trim()) {
      newErrors.tractorWeightKg = 'Tractor weight is required';
    } else if (isNaN(parseFloat(formData.tractorWeightKg)) || parseFloat(formData.tractorWeightKg) <= 0) {
      newErrors.tractorWeightKg = 'Tractor weight must be a positive number';
    }

    if (formData.driverPhone && !/^\+?[\d\s-()]+$/.test(formData.driverPhone)) {
      newErrors.driverPhone = 'Invalid phone number format';
    }

    if (formData.ownerPhone && !/^\+?[\d\s-()]+$/.test(formData.ownerPhone)) {
      newErrors.ownerPhone = 'Invalid phone number format';
    }

    if (formData.capacityTons && (isNaN(formData.capacityTons) || parseFloat(formData.capacityTons) <= 0)) {
      newErrors.capacityTons = 'Capacity must be a positive number';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (validateForm()) {
      const processedData = {
        ...formData,
        vehicleNumber: formData.vehicleNumber.trim().toUpperCase(),
        capacityTons: formData.capacityTons ? parseFloat(formData.capacityTons) : null,
        tractorWeightKg: parseFloat(formData.tractorWeightKg),
      };
      
      onSubmit(processedData);
    }
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Vehicle Information Card */}
        <Card style={styles.sectionCard} elevation={3}>
          <Card.Content>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionIcon}>🚜</Text>
              <Text style={styles.sectionTitle}>Vehicle Information</Text>
            </View>

            <View style={styles.inputGroup}>
              <TextInput
                label="Vehicle Number *"
                value={formData.vehicleNumber}
                onChangeText={(value) => handleInputChange('vehicleNumber', value)}
                style={styles.input}
                error={!!errors.vehicleNumber}
                mode="outlined"
                autoCapitalize="characters"
                placeholder="AP 01 AB 1234"
                theme={{ colors: { primary: Colors.primary } }}
                contentStyle={styles.inputContent}
              />
              {errors.vehicleNumber && <Text style={styles.errorText}>{errors.vehicleNumber}</Text>}

              <View style={styles.row}>
                <TextInput
                  label="Brand"
                  value={formData.brand}
                  onChangeText={(value) => handleInputChange('brand', value)}
                  style={[styles.input, styles.inputHalf]}
                  mode="outlined"
                  placeholder="Mahindra, John Deere, etc."
                  theme={{ colors: { primary: Colors.primary } }}
                  contentStyle={styles.inputContent}
                />
                <TextInput
                  label="Model"
                  value={formData.model}
                  onChangeText={(value) => handleInputChange('model', value)}
                  style={[styles.input, styles.inputHalf]}
                  mode="outlined"
                  placeholder="575 DI, 5050E, etc."
                  theme={{ colors: { primary: Colors.primary } }}
                  contentStyle={styles.inputContent}
                />
              </View>

              <View style={styles.row}>
                <TextInput
                  label="Capacity (Tons)"
                  value={formData.capacityTons}
                  onChangeText={(value) => handleInputChange('capacityTons', value)}
                  style={[styles.input, styles.inputHalf]}
                  error={!!errors.capacityTons}
                  keyboardType="numeric"
                  mode="outlined"
                  placeholder="10.5"
                  theme={{ colors: { primary: Colors.primary } }}
                  contentStyle={styles.inputContent}
                />
                <TextInput
                  label="Tractor Weight (Kg) *"
                  value={formData.tractorWeightKg}
                  onChangeText={(value) => handleInputChange('tractorWeightKg', value)}
                  style={[styles.input, styles.inputHalf]}
                  error={!!errors.tractorWeightKg}
                  keyboardType="numeric"
                  mode="outlined"
                  placeholder="2500"
                  theme={{ colors: { primary: Colors.primary } }}
                  contentStyle={styles.inputContent}
                />
              </View>
              {errors.capacityTons && <Text style={styles.errorText}>{errors.capacityTons}</Text>}
              {errors.tractorWeightKg && <Text style={styles.errorText}>{errors.tractorWeightKg}</Text>}
            </View>
          </Card.Content>
        </Card>

        {/* Driver Information Card */}
        <Card style={styles.sectionCard} elevation={3}>
          <Card.Content>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionIcon}>👨‍💼</Text>
              <Text style={styles.sectionTitle}>Driver Information</Text>
            </View>

            <View style={styles.inputGroup}>
              <TextInput
                label="Driver Name"
                value={formData.driverName}
                onChangeText={(value) => handleInputChange('driverName', value)}
                style={styles.input}
                mode="outlined"
                theme={{ colors: { primary: Colors.primary } }}
                contentStyle={styles.inputContent}
              />

              <View style={styles.row}>
                <TextInput
                  label="Driver Phone"
                  value={formData.driverPhone}
                  onChangeText={(value) => handleInputChange('driverPhone', value)}
                  style={[styles.input, styles.inputHalf]}
                  error={!!errors.driverPhone}
                  keyboardType="phone-pad"
                  mode="outlined"
                  theme={{ colors: { primary: Colors.primary } }}
                  contentStyle={styles.inputContent}
                />
                <TextInput
                  label="License Number"
                  value={formData.driverLicense}
                  onChangeText={(value) => handleInputChange('driverLicense', value)}
                  style={[styles.input, styles.inputHalf]}
                  mode="outlined"
                  theme={{ colors: { primary: Colors.primary } }}
                  contentStyle={styles.inputContent}
                />
              </View>
              {errors.driverPhone && <Text style={styles.errorText}>{errors.driverPhone}</Text>}
            </View>
          </Card.Content>
        </Card>

        {/* Owner Information Card */}
        <Card style={styles.sectionCard} elevation={3}>
          <Card.Content>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionIcon}>👤</Text>
              <Text style={styles.sectionTitle}>Owner Information</Text>
            </View>

            <View style={styles.inputGroup}>
              <TextInput
                label="Owner Name"
                value={formData.ownerName}
                onChangeText={(value) => handleInputChange('ownerName', value)}
                style={styles.input}
                mode="outlined"
                theme={{ colors: { primary: Colors.primary } }}
                contentStyle={styles.inputContent}
              />

              <TextInput
                label="Owner Phone"
                value={formData.ownerPhone}
                onChangeText={(value) => handleInputChange('ownerPhone', value)}
                style={styles.input}
                error={!!errors.ownerPhone}
                keyboardType="phone-pad"
                mode="outlined"
                theme={{ colors: { primary: Colors.primary } }}
                contentStyle={styles.inputContent}
              />
              {errors.ownerPhone && <Text style={styles.errorText}>{errors.ownerPhone}</Text>}

              <TextInput
                label="Owner Address"
                value={formData.ownerAddress}
                onChangeText={(value) => handleInputChange('ownerAddress', value)}
                style={styles.input}
                mode="outlined"
                multiline
                numberOfLines={2}
                theme={{ colors: { primary: Colors.primary } }}
                contentStyle={styles.inputContent}
              />
            </View>
          </Card.Content>
        </Card>

        {/* Document Information Card */}
        <Card style={styles.sectionCard} elevation={3}>
          <Card.Content>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionIcon}>📄</Text>
              <Text style={styles.sectionTitle}>Document Information</Text>
            </View>

            <View style={styles.inputGroup}>
              <TextInput
                label="Registration Date"
                value={formData.registrationDate}
                onChangeText={(value) => handleInputChange('registrationDate', value)}
                style={styles.input}
                mode="outlined"
                placeholder="YYYY-MM-DD"
                theme={{ colors: { primary: Colors.primary } }}
                contentStyle={styles.inputContent}
              />

              <View style={styles.row}>
                <TextInput
                  label="Insurance Expiry"
                  value={formData.insuranceExpiry}
                  onChangeText={(value) => handleInputChange('insuranceExpiry', value)}
                  style={[styles.input, styles.inputHalf]}
                  mode="outlined"
                  placeholder="YYYY-MM-DD"
                  theme={{ colors: { primary: Colors.primary } }}
                  contentStyle={styles.inputContent}
                />
                <TextInput
                  label="Fitness Expiry"
                  value={formData.fitnessExpiry}
                  onChangeText={(value) => handleInputChange('fitnessExpiry', value)}
                  style={[styles.input, styles.inputHalf]}
                  mode="outlined"
                  placeholder="YYYY-MM-DD"
                  theme={{ colors: { primary: Colors.primary } }}
                  contentStyle={styles.inputContent}
                />
              </View>

              <TextInput
                label="Notes"
                value={formData.notes}
                onChangeText={(value) => handleInputChange('notes', value)}
                style={styles.input}
                mode="outlined"
                multiline
                numberOfLines={3}
                placeholder="Any additional notes about the tractor..."
                theme={{ colors: { primary: Colors.primary } }}
                contentStyle={styles.inputContent}
              />
            </View>
          </Card.Content>
        </Card>

        {/* Action Buttons Card */}
        <Card style={styles.actionCard} elevation={3}>
          <Card.Content>
            <View style={styles.buttonContainer}>
              <Button
                mode="contained"
                onPress={handleSubmit}
                style={styles.submitButton}
                contentStyle={styles.buttonContent}
                labelStyle={styles.buttonLabel}
                buttonColor={Colors.primary}
                textColor={Colors.secondary}
                icon="content-save"
              >
                Save Tractor
              </Button>

              <Button
                mode="outlined"
                onPress={onCancel}
                style={styles.cancelButton}
                contentStyle={styles.buttonContent}
                labelStyle={styles.cancelButtonLabel}
                icon="cancel"
              >
                Cancel
              </Button>
            </View>
          </Card.Content>
        </Card>
      </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    paddingTop: Spacing.lg,
  },
  // Section Cards
  sectionCard: {
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
    borderRadius: BorderRadius.xl,
    backgroundColor: Colors.surface,
    borderWidth: 1,
    borderColor: Colors.primaryLowOpacity,
    shadowColor: Colors.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },
  sectionIcon: {
    fontSize: 20,
    marginRight: Spacing.sm,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.primary,
  },
  inputGroup: {
    gap: Spacing.md,
  },
  input: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.md,
    borderWidth: 1,
    borderColor: Colors.secondaryLowOpacity,
    marginBottom: Spacing.sm,
  },
  inputContent: {
    fontSize: Typography.fontSize.md,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: Spacing.md,
  },
  inputHalf: {
    flex: 1,
  },
  errorText: {
    color: Colors.error,
    fontSize: Typography.fontSize.sm,
    marginTop: Spacing.xs,
    marginLeft: Spacing.sm,
  },
  // Action Card
  actionCard: {
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.xl,
    borderRadius: BorderRadius.xl,
    backgroundColor: Colors.surface,
    borderWidth: 1,
    borderColor: Colors.primaryLowOpacity,
    shadowColor: Colors.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
  },
  buttonContainer: {
    gap: Spacing.md,
  },
  submitButton: {
    backgroundColor: Colors.primary,
    borderRadius: BorderRadius.lg,
  },
  cancelButton: {
    borderColor: Colors.error,
    borderRadius: BorderRadius.lg,
  },
  buttonContent: {
    paddingVertical: Spacing.sm,
  },
  buttonLabel: {
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.secondary, // Yellow text on green button
  },
  cancelButtonLabel: {
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.error,
  },
});

export default TractorForm;
