import React, { useState } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import {
  TextInput,
  Button,
  Card,
  Text,
  SegmentedButtons
} from 'react-native-paper';
import { Colors, Spacing, BorderRadius, Typography } from '../constants/theme';

const FarmerForm = ({ 
  onSubmit, 
  onCancel, 
  initialData = {},
  title = "Add Farmer"
}) => {
  const [formData, setFormData] = useState({
    name: (initialData && initialData.name) || '',
    type: (initialData && initialData.type) || 'buyer',
    phone: (initialData && initialData.phone) || '',
    address: (initialData && initialData.address) || '',
    village: (initialData && initialData.village) || '',
    district: (initialData && initialData.district) || '',
    state: (initialData && initialData.state) || '',
    pincode: (initialData && initialData.pincode) || '',
    aadharNumber: (initialData && initialData.aadhar_number) || '',
    panNumber: (initialData && initialData.pan_number) || '',
    bankAccount: (initialData && initialData.bank_account) || '',
    ifscCode: (initialData && initialData.ifsc_code) || '',
    notes: (initialData && initialData.notes) || '',
  });

  const [errors, setErrors] = useState({});

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }
    
    if (formData.phone && !/^\+?[\d\s-()]+$/.test(formData.phone)) {
      newErrors.phone = 'Invalid phone number format';
    }
    
    if (formData.pincode && !/^\d{6}$/.test(formData.pincode)) {
      newErrors.pincode = 'Pincode must be 6 digits';
    }
    
    if (formData.aadharNumber && !/^\d{12}$/.test(formData.aadharNumber.replace(/\s/g, ''))) {
      newErrors.aadharNumber = 'Aadhar number must be 12 digits';
    }
    
    if (formData.panNumber && !/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/.test(formData.panNumber.toUpperCase())) {
      newErrors.panNumber = 'Invalid PAN format (e.g., **********)';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (validateForm()) {
      const processedData = {
        ...formData,
        name: formData.name.trim(),
        panNumber: formData.panNumber.toUpperCase(),
        aadharNumber: formData.aadharNumber.replace(/\s/g, ''),
      };
      
      onSubmit(processedData);
    }
  };

  const typeOptions = [
    { value: 'buyer', label: '🛒 Buyer' },
    { value: 'seller', label: '🌾 Seller' },
    { value: 'both', label: '🔄 Both' }
  ];

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Basic Information Card */}
        <Card style={styles.sectionCard} elevation={3}>
          <Card.Content>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionIcon}>👤</Text>
              <Text style={styles.sectionTitle}>Basic Information</Text>
            </View>

            <View style={styles.inputGroup}>
              <TextInput
                label="Full Name *"
                value={formData.name}
                onChangeText={(value) => handleInputChange('name', value)}
                style={styles.input}
                error={!!errors.name}
                mode="outlined"
                theme={{ colors: { primary: Colors.primary } }}
                contentStyle={styles.inputContent}
              />
              {errors.name && <Text style={styles.errorText}>{errors.name}</Text>}

              <View style={styles.typeSection}>
                <Text style={styles.inputLabel}>Farmer Type *</Text>
                <SegmentedButtons
                  value={formData.type}
                  onValueChange={(value) => handleInputChange('type', value)}
                  buttons={typeOptions}
                  style={styles.segmentedButtons}
                />
              </View>

              <TextInput
                label="Phone Number"
                value={formData.phone}
                onChangeText={(value) => handleInputChange('phone', value)}
                style={styles.input}
                error={!!errors.phone}
                keyboardType="phone-pad"
                theme={{ colors: { primary: Colors.primary } }}
                contentStyle={styles.inputContent}
                mode="outlined"
              />
              {errors.phone && <Text style={styles.errorText}>{errors.phone}</Text>}
            </View>
          </Card.Content>
        </Card>

        {/* Address Information Card */}
        <Card style={styles.sectionCard} elevation={3}>
          <Card.Content>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionIcon}>📍</Text>
              <Text style={styles.sectionTitle}>Address Information</Text>
            </View>

            <View style={styles.inputGroup}>
              <TextInput
                label="Address"
                value={formData.address}
                onChangeText={(value) => handleInputChange('address', value)}
                style={styles.input}
                mode="outlined"
                multiline
                numberOfLines={2}
                theme={{ colors: { primary: Colors.primary } }}
                contentStyle={styles.inputContent}
              />

              <View style={styles.row}>
                <TextInput
                  label="Village"
                  value={formData.village}
                  onChangeText={(value) => handleInputChange('village', value)}
                  style={[styles.input, styles.inputHalf]}
                  mode="outlined"
                  theme={{ colors: { primary: Colors.primary } }}
                  contentStyle={styles.inputContent}
                />
                <TextInput
                  label="District"
                  value={formData.district}
                  onChangeText={(value) => handleInputChange('district', value)}
                  style={[styles.input, styles.inputHalf]}
                  mode="outlined"
                  theme={{ colors: { primary: Colors.primary } }}
                  contentStyle={styles.inputContent}
                />
              </View>

              <View style={styles.row}>
                <TextInput
                  label="State"
                  value={formData.state}
                  onChangeText={(value) => handleInputChange('state', value)}
                  style={[styles.input, styles.inputHalf]}
                  mode="outlined"
                  theme={{ colors: { primary: Colors.primary } }}
                  contentStyle={styles.inputContent}
                />
                <TextInput
                  label="Pincode"
                  value={formData.pincode}
                  onChangeText={(value) => handleInputChange('pincode', value)}
                  style={[styles.input, styles.inputHalf]}
                  error={!!errors.pincode}
                  keyboardType="numeric"
                  mode="outlined"
                  theme={{ colors: { primary: Colors.primary } }}
                  contentStyle={styles.inputContent}
                />
              </View>
              {errors.pincode && <Text style={styles.errorText}>{errors.pincode}</Text>}
            </View>
          </Card.Content>
        </Card>

        {/* Identity Information Card */}
        <Card style={styles.sectionCard} elevation={3}>
          <Card.Content>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionIcon}>🆔</Text>
              <Text style={styles.sectionTitle}>Identity Information</Text>
            </View>

            <View style={styles.inputGroup}>
              <TextInput
                label="Aadhar Number"
                value={formData.aadharNumber}
                onChangeText={(value) => handleInputChange('aadharNumber', value)}
                style={styles.input}
                error={!!errors.aadharNumber}
                keyboardType="numeric"
                mode="outlined"
                placeholder="1234 5678 9012"
                theme={{ colors: { primary: Colors.primary } }}
                contentStyle={styles.inputContent}
              />
              {errors.aadharNumber && <Text style={styles.errorText}>{errors.aadharNumber}</Text>}

              <TextInput
                label="PAN Number"
                value={formData.panNumber}
                onChangeText={(value) => handleInputChange('panNumber', value)}
                style={styles.input}
                error={!!errors.panNumber}
                mode="outlined"
                placeholder="**********"
                autoCapitalize="characters"
                theme={{ colors: { primary: Colors.primary } }}
                contentStyle={styles.inputContent}
              />
              {errors.panNumber && <Text style={styles.errorText}>{errors.panNumber}</Text>}
            </View>
          </Card.Content>
        </Card>

        {/* Banking Information Card */}
        <Card style={styles.sectionCard} elevation={3}>
          <Card.Content>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionIcon}>🏦</Text>
              <Text style={styles.sectionTitle}>Banking Information</Text>
            </View>

            <View style={styles.inputGroup}>
              <TextInput
                label="Bank Account Number"
                value={formData.bankAccount}
                onChangeText={(value) => handleInputChange('bankAccount', value)}
                style={styles.input}
                keyboardType="numeric"
                mode="outlined"
                theme={{ colors: { primary: Colors.primary } }}
                contentStyle={styles.inputContent}
              />

              <TextInput
                label="IFSC Code"
                value={formData.ifscCode}
                onChangeText={(value) => handleInputChange('ifscCode', value)}
                style={styles.input}
                mode="outlined"
                autoCapitalize="characters"
                placeholder="ABCD0123456"
                theme={{ colors: { primary: Colors.primary } }}
                contentStyle={styles.inputContent}
              />

              <TextInput
                label="Notes"
                value={formData.notes}
                onChangeText={(value) => handleInputChange('notes', value)}
                style={styles.input}
                mode="outlined"
                multiline
                numberOfLines={3}
                placeholder="Any additional notes..."
                theme={{ colors: { primary: Colors.primary } }}
                contentStyle={styles.inputContent}
              />
            </View>
          </Card.Content>
        </Card>

        {/* Action Buttons Card */}
        <Card style={styles.actionCard} elevation={3}>
          <Card.Content>
            <View style={styles.buttonContainer}>
              <Button
                mode="contained"
                onPress={handleSubmit}
                style={styles.submitButton}
                contentStyle={styles.buttonContent}
                labelStyle={styles.buttonLabel}
                buttonColor={Colors.primary}
                textColor={Colors.secondary}
                icon="content-save"
              >
                Save Farmer
              </Button>

              <Button
                mode="outlined"
                onPress={onCancel}
                style={styles.cancelButton}
                contentStyle={styles.buttonContent}
                labelStyle={styles.cancelButtonLabel}
                icon="cancel"
              >
                Cancel
              </Button>
            </View>
          </Card.Content>
        </Card>
      </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    paddingTop: Spacing.lg,
  },
  // Section Cards
  sectionCard: {
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
    borderRadius: BorderRadius.xl,
    backgroundColor: Colors.surface,
    borderWidth: 1,
    borderColor: Colors.primaryLowOpacity,
    shadowColor: Colors.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },
  sectionIcon: {
    fontSize: 20,
    marginRight: Spacing.sm,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.primary,
  },
  inputGroup: {
    gap: Spacing.md,
  },
  input: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.md,
    borderWidth: 1,
    borderColor: Colors.secondaryLowOpacity,
    marginBottom: Spacing.sm,
  },
  inputContent: {
    fontSize: Typography.fontSize.md,
  },
  inputLabel: {
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.medium,
    color: Colors.text,
    marginBottom: Spacing.sm,
  },
  typeSection: {
    marginVertical: Spacing.sm,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: Spacing.md,
  },
  inputHalf: {
    flex: 1,
  },
  segmentedButtons: {
    marginTop: Spacing.xs,
  },
  errorText: {
    color: Colors.error,
    fontSize: Typography.fontSize.sm,
    marginTop: Spacing.xs,
    marginLeft: Spacing.sm,
  },
  // Action Card
  actionCard: {
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.xl,
    borderRadius: BorderRadius.xl,
    backgroundColor: Colors.surface,
    borderWidth: 1,
    borderColor: Colors.primaryLowOpacity,
    shadowColor: Colors.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
  },
  buttonContainer: {
    gap: Spacing.md,
  },
  submitButton: {
    backgroundColor: Colors.primary,
    borderRadius: BorderRadius.lg,
  },
  cancelButton: {
    borderColor: Colors.error,
    borderRadius: BorderRadius.lg,
  },
  buttonContent: {
    paddingVertical: Spacing.sm,
  },
  buttonLabel: {
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.secondary, // Yellow text on green button
  },
  cancelButtonLabel: {
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.error,
  },
});

export default FarmerForm;
