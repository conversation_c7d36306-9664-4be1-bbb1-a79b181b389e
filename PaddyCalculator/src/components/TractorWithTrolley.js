import React from 'react';
import { View } from 'react-native';
import Svg, { Path, Circle, Rect, G } from 'react-native-svg';

const TractorWithTrolley = ({ size = 24, color = '#2E7D32' }) => {
  return (
    <View style={{ width: size, height: size }}>
      <Svg width={size} height={size} viewBox="0 0 48 24" fill="none">
        {/* Trolley/Trailer (Behind) */}
        <G>
          {/* Trolley Body */}
          <Rect x="2" y="6" width="16" height="10" rx="2" fill={color} opacity="0.9" />

          {/* Trolley Side Details */}
          <Rect x="4" y="8" width="12" height="2" fill="#4CAF50" />
          <Rect x="4" y="12" width="12" height="2" fill="#4CAF50" />

          {/* Trolley Wheels */}
          <Circle cx="8" cy="18" r="3" fill="#333" stroke={color} strokeWidth="1" />
          <Circle cx="14" cy="18" r="3" fill="#333" stroke={color} strokeWidth="1" />

          {/* Trolley Front */}
          <Rect x="18" y="8" width="2" height="8" fill={color} />
        </G>

        {/* Connection Bar */}
        <Rect x="20" y="11" width="6" height="2" fill={color} />

        {/* Tractor Body (In Front) */}
        <G>
          {/* Main Tractor Body */}
          <Rect x="26" y="8" width="16" height="8" rx="2" fill={color} />

          {/* Tractor Cabin */}
          <Rect x="28" y="4" width="8" height="8" rx="1" fill={color} />

          {/* Tractor Front */}
          <Rect x="42" y="10" width="4" height="4" rx="1" fill={color} />

          {/* Tractor Wheels */}
          <Circle cx="30" cy="18" r="3" fill="#333" stroke={color} strokeWidth="1" />
          <Circle cx="38" cy="18" r="3" fill="#333" stroke={color} strokeWidth="1" />

          {/* Smaller front wheel */}
          <Circle cx="44" cy="17" r="2" fill="#333" stroke={color} strokeWidth="1" />

          {/* Tractor Details */}
          <Rect x="30" y="6" width="2" height="2" fill="#FFD700" />
          <Rect x="33" y="6" width="1" height="2" fill="#FFD700" />
        </G>

        {/* Motion Lines (to show forward movement) */}
        <G opacity="0.6">
          <Path d="M44 6 L48 6" stroke={color} strokeWidth="1" strokeLinecap="round" />
          <Path d="M42 10 L48 10" stroke={color} strokeWidth="1" strokeLinecap="round" />
          <Path d="M44 14 L48 14" stroke={color} strokeWidth="1" strokeLinecap="round" />
        </G>
      </Svg>
    </View>
  );
};

export default TractorWithTrolley;
