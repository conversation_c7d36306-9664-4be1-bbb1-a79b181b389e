import React, { useState, useEffect } from 'react';
import { View, StyleSheet, TouchableOpacity, Modal, FlatList } from 'react-native';
import { TextInput, Button, Card, Text, IconButton, Divider } from 'react-native-paper';
import { Colors, Spacing, BorderRadius } from '../constants/theme';
import { getAllFarmers, getAllTractors } from '../database/database';

const EntryForm = ({
  onSubmit,
  onRemove,
  serialNumber,
  initialData = {},
  showRemoveButton = true,
  refreshTrigger = 0,
  isFirstEntry = false,
  globalBuyerId = null,
  globalSellerId = null,
  globalPricePer900 = '',
  globalRewashPercentage = '2.0',
  onGlobalFieldChange = null,
  isExpanded = false,
  onExpandToggle = null
}) => {
  const [focusedField, setFocusedField] = useState(null);
  const [formData, setFormData] = useState({
    billNo: initialData.billNo || '',
    buyerId: globalBuyerId || initialData.buyerId || null,
    sellerId: globalSellerId || initialData.sellerId || null,
    tractorId: initialData.tractorId || null,
    tractorNo: initialData.tractorNo || '',
    grossWeight: initialData.grossWeight?.toString() || '',
    tractorWeight: initialData.tractorWeight?.toString() || '',
    rewashPercentage: globalRewashPercentage || initialData.rewashPercentage?.toString() || '2.0',
    pricePer900: globalPricePer900 || initialData.pricePer900?.toString() || '',
  });

  const [errors, setErrors] = useState({});
  const [buyers, setBuyers] = useState([]);
  const [sellers, setSellers] = useState([]);
  const [tractors, setTractors] = useState([]);
  const [buyerModalVisible, setBuyerModalVisible] = useState(false);
  const [sellerModalVisible, setSellerModalVisible] = useState(false);
  const [tractorModalVisible, setTractorModalVisible] = useState(false);
  const [buyerSearchQuery, setBuyerSearchQuery] = useState('');
  const [sellerSearchQuery, setSellerSearchQuery] = useState('');
  const [tractorSearchQuery, setTractorSearchQuery] = useState('');

  // Debug modal state changes
  useEffect(() => {
    
  }, [buyerModalVisible]);

  useEffect(() => {
    
  }, [sellerModalVisible]);

  // Load farmers and tractors on component mount and when refreshTrigger changes
  useEffect(() => {
    loadData();
  }, [refreshTrigger]);

  // Sync global field changes to form data (for all entries)
  useEffect(() => {
    const newFormData = {
      ...formData,
      buyerId: globalBuyerId,
      sellerId: globalSellerId,
      pricePer900: globalPricePer900,
      rewashPercentage: globalRewashPercentage
    };

    console.log('🔄 Syncing global fields to entry', serialNumber, ':', {
      buyerId: globalBuyerId,
      sellerId: globalSellerId,
      pricePer900: globalPricePer900,
      rewashPercentage: globalRewashPercentage
    });

    setFormData(newFormData);

    // Auto-submit updated data
    if (onSubmit) {
      onSubmit(newFormData);
    }
  }, [globalBuyerId, globalSellerId, globalPricePer900, globalRewashPercentage]);

  const loadData = async () => {
    try {
      // Add a small delay to ensure database is initialized
      await new Promise(resolve => setTimeout(resolve, 100));

      const [buyersData, sellersData, tractorsData] = await Promise.all([
        getAllFarmers('buyer').catch(() => []),
        getAllFarmers('seller').catch(() => []),
        getAllTractors().catch(() => [])
      ]);
      setBuyers(buyersData || []);
      setSellers(sellersData || []);
      setTractors(tractorsData || []);
    } catch (error) {
      
      // Set empty arrays as fallback
      setBuyers([]);
      setSellers([]);
      setTractors([]);
    }
  };

  const handleInputChange = (field, value) => {
    // Check if this is a global field (buyer, seller, price, rewash)
    const globalFields = ['buyerId', 'sellerId', 'pricePer900', 'rewashPercentage'];
    const isGlobalField = globalFields.includes(field);

    // Validate buyer/seller selection
    if (field === 'buyerId' || field === 'sellerId') {
      const otherField = field === 'buyerId' ? 'sellerId' : 'buyerId';
      const otherValue = formData[otherField];

      // Check if same person selected for both buyer and seller
      if (value && value === otherValue) {
        setErrors(prev => ({
          ...prev,
          [field]: 'Buyer and Seller cannot be the same person'
        }));
        return;
      }
    }

    // If this is a global field and first entry, propagate to all entries
    if (isGlobalField && isFirstEntry && onGlobalFieldChange) {
      onGlobalFieldChange(field, value);
      // Note: Don't return here, let it continue to update local form data too
    }

    // For non-global fields or non-first entries, prevent editing global fields
    if (isGlobalField && !isFirstEntry) {
      // Don't allow editing global fields in non-first entries
      return;
    }

    const newFormData = {
      ...formData,
      [field]: value
    };

    setFormData(newFormData);

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }

    // Auto-submit data changes to parent component
    if (onSubmit) {
      onSubmit(newFormData);
    }
  };





  return (
    <Card style={styles.card}>
      <Card.Content>
        <View style={styles.header}>
          <View style={styles.entryBadge}>
            <Text style={styles.entryBadgeText}>{serialNumber}</Text>
          </View>
          <Text style={styles.entryTitle}>
            {isExpanded ? 'Entry Details' : (formData.billNo || 'No Receipt No.')}
          </Text>
          <View style={styles.headerActions}>
            <IconButton
              icon={isExpanded ? "chevron-up" : "chevron-down"}
              iconColor={Colors.primary}
              size={20}
              onPress={() => onExpandToggle && onExpandToggle()}
              style={styles.expandButton}
            />
            {showRemoveButton && (
              <IconButton
                icon="delete"
                iconColor={Colors.error}
                size={20}
                onPress={onRemove}
                style={styles.deleteButton}
              />
            )}
          </View>
        </View>

        {isExpanded && (
          <>
            <TextInput
              label="Receipt No."
              value={formData.billNo}
              onChangeText={(value) => handleInputChange('billNo', value)}
              onFocus={() => setFocusedField('billNo')}
              onBlur={() => setFocusedField(null)}
              style={[styles.input, focusedField === 'billNo' && styles.inputFocused]}
              error={!!errors.billNo}
              mode="outlined"
              placeholder="Enter unique receipt number"
            />
            {errors.billNo && <Text style={styles.errorText}>{errors.billNo}</Text>}

        <Divider style={styles.divider} />

        {/* Farmer and Tractor Selection */}
        <Text style={styles.sectionTitle}>👥 Farmer & Tractor Selection</Text>

        {/* Buyer Selection */}
        <TouchableOpacity
          onPress={() => {
            
            if (isFirstEntry) {
              
              setBuyerModalVisible(true);
            } else {
              
            }
          }}
          disabled={!isFirstEntry}
          style={styles.touchableField}
        >
          <TextInput
            label={`Buyer (Customer)${!isFirstEntry ? ' - Set in Entry 1' : ''}`}
            value={(() => {
              const selectedBuyer = buyers.find(b => b.id === formData.buyerId);
              const displayValue = selectedBuyer?.name || 'Select Buyer';
              console.log(`🔍 Entry ${serialNumber} buyer display:`, {
                buyerId: formData.buyerId,
                selectedBuyer: selectedBuyer?.name,
                displayValue,
                buyersCount: buyers.length
              });
              return displayValue;
            })()}
            style={[styles.input, styles.dropdownInput, !isFirstEntry && styles.disabledInput]}
            mode="outlined"
            right={<TextInput.Icon icon={isFirstEntry ? "chevron-down" : "lock"} />}
            editable={false}
            pointerEvents="none"
          />
        </TouchableOpacity>
        {errors.buyerId && <Text style={styles.errorText}>{errors.buyerId}</Text>}

        {/* Seller Selection */}
        <TouchableOpacity
          onPress={() => {
            
            if (isFirstEntry) {
              
              setSellerModalVisible(true);
            } else {
              
            }
          }}
          disabled={!isFirstEntry}
          style={styles.touchableField}
        >
          <TextInput
            label={`Seller (Farmer)${!isFirstEntry ? ' - Set in Entry 1' : ''}`}
            value={sellers.find(s => s.id === formData.sellerId)?.name || 'Select Seller'}
            style={[styles.input, styles.dropdownInput, !isFirstEntry && styles.disabledInput]}
            mode="outlined"
            right={<TextInput.Icon icon={isFirstEntry ? "chevron-down" : "lock"} />}
            editable={false}
            pointerEvents="none"
          />
        </TouchableOpacity>
        {errors.sellerId && <Text style={styles.errorText}>{errors.sellerId}</Text>}

        {/* Tractor Selection */}
        <TouchableOpacity onPress={() => setTractorModalVisible(true)}>
          <TextInput
            label="Tractor"
            value={
              formData.tractorId
                ? tractors.find(t => t.id === formData.tractorId)?.vehicle_number || 'Select Tractor'
                : 'Select Tractor'
            }
            style={[styles.input, styles.dropdownInput]}
            mode="outlined"
            right={<TextInput.Icon icon="chevron-down" />}
            editable={false}
            pointerEvents="none"
          />
        </TouchableOpacity>



        <Divider style={styles.divider} />
        
        <View style={styles.row}>
          <TextInput
            label="Gross Weight (kg)"
            value={formData.grossWeight}
            onChangeText={(value) => handleInputChange('grossWeight', value)}
            onFocus={() => setFocusedField('grossWeight')}
            onBlur={() => setFocusedField(null)}
            style={[styles.input, styles.inputHalf, focusedField === 'grossWeight' && styles.inputFocused]}
            error={!!errors.grossWeight}
            keyboardType="numeric"
            mode="outlined"
          />
          <TextInput
            label="Tractor Weight (kg)"
            value={formData.tractorWeight}
            onChangeText={(value) => handleInputChange('tractorWeight', value)}
            onFocus={() => setFocusedField('tractorWeight')}
            onBlur={() => setFocusedField(null)}
            style={[styles.input, styles.inputHalf, focusedField === 'tractorWeight' && styles.inputFocused]}
            error={!!errors.tractorWeight}
            keyboardType="numeric"
            mode="outlined"
          />
        </View>
        
        {errors.grossWeight && <Text style={styles.errorText}>{errors.grossWeight}</Text>}
        {errors.tractorWeight && <Text style={styles.errorText}>{errors.tractorWeight}</Text>}
        
        <View style={styles.row}>
          <TextInput
            label={`Rewash (%)${!isFirstEntry ? ' - Set in Entry 1' : ''}`}
            value={formData.rewashPercentage}
            onChangeText={(value) => handleInputChange('rewashPercentage', value)}
            onFocus={() => setFocusedField('rewashPercentage')}
            onBlur={() => setFocusedField(null)}
            style={[
              styles.input,
              styles.inputHalf,
              !isFirstEntry && styles.disabledInput,
              focusedField === 'rewashPercentage' && isFirstEntry && styles.inputFocused
            ]}
            error={!!errors.rewashPercentage}
            keyboardType="numeric"
            mode="outlined"
            editable={isFirstEntry}
            right={!isFirstEntry ? <TextInput.Icon icon="lock" /> : null}
          />
          <TextInput
            label={`Price/900kg${!isFirstEntry ? ' - Set in Entry 1' : ''}`}
            value={formData.pricePer900}
            onChangeText={(value) => handleInputChange('pricePer900', value)}
            onFocus={() => setFocusedField('pricePer900')}
            onBlur={() => setFocusedField(null)}
            style={[
              styles.input,
              styles.inputHalf,
              !isFirstEntry && styles.disabledInput,
              focusedField === 'pricePer900' && isFirstEntry && styles.inputFocused
            ]}
            error={!!errors.pricePer900}
            keyboardType="numeric"
            mode="outlined"
            editable={isFirstEntry}
            right={!isFirstEntry ? <TextInput.Icon icon="lock" /> : null}
          />
        </View>
        
            {errors.rewashPercentage && <Text style={styles.errorText}>{errors.rewashPercentage}</Text>}
            {errors.pricePer900 && <Text style={styles.errorText}>{errors.pricePer900}</Text>}
          </>
        )}
      </Card.Content>

      {/* Buyer Selection Modal */}
      <Modal
        visible={buyerModalVisible}
        onRequestClose={() => {
          
          setBuyerModalVisible(false);
          setBuyerSearchQuery('');
        }}
        animationType="slide"
        transparent={true}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Select Buyer</Text>
            <TextInput
              label="Search buyers..."
              value={buyerSearchQuery}
              onChangeText={setBuyerSearchQuery}
              style={styles.searchInput}
              mode="outlined"
              left={<TextInput.Icon icon="magnify" />}
            />
            <FlatList
              data={[
                { id: null, name: 'None', type: '' },
                ...buyers.filter(buyer =>
                  buyer.name.toLowerCase().includes(buyerSearchQuery.toLowerCase()) ||
                  (buyer.type && buyer.type.toLowerCase().includes(buyerSearchQuery.toLowerCase()))
                )
              ]}
              keyExtractor={(item) => item.id?.toString() || 'none'}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={styles.modalItem}
                  onPress={() => {
                    

                    // Validate buyer/seller selection
                    if (item.id && item.id === formData.sellerId) {
                      setErrors(prev => ({
                        ...prev,
                        buyerId: 'Buyer and Seller cannot be the same person'
                      }));
                      setBuyerModalVisible(false);
                      return;
                    }

                    // Clear any existing error
                    if (errors.buyerId) {
                      setErrors(prev => ({
                        ...prev,
                        buyerId: null
                      }));
                    }

                    // Update local form data immediately
                    const newFormData = {
                      ...formData,
                      buyerId: item.id
                    };
                    setFormData(newFormData);

                    // Auto-submit to parent
                    if (onSubmit) {
                      onSubmit(newFormData);
                    }

                    // If this is first entry, also use global handler
                    if (isFirstEntry && onGlobalFieldChange) {
                      
                      onGlobalFieldChange('buyerId', item.id);
                    } else {
                      // For non-first entries, this should not happen as they're disabled
                      
                    }

                    
                    setBuyerModalVisible(false);
                    setBuyerSearchQuery('');
                  }}
                >
                  <Text style={styles.modalItemText}>
                    {item.name} {item.type && `(${item.type})`}
                  </Text>
                </TouchableOpacity>
              )}
            />
            <Button onPress={() => {
              setBuyerModalVisible(false);
              setBuyerSearchQuery('');
            }}>Cancel</Button>
          </View>
        </View>
      </Modal>

      {/* Seller Selection Modal */}
      <Modal
        visible={sellerModalVisible}
        onRequestClose={() => {
          setSellerModalVisible(false);
          setSellerSearchQuery('');
        }}
        animationType="slide"
        transparent={true}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Select Seller</Text>
            <TextInput
              label="Search sellers..."
              value={sellerSearchQuery}
              onChangeText={setSellerSearchQuery}
              style={styles.searchInput}
              mode="outlined"
              left={<TextInput.Icon icon="magnify" />}
            />
            <FlatList
              data={[
                { id: null, name: 'None', type: '' },
                ...sellers.filter(seller =>
                  seller.name.toLowerCase().includes(sellerSearchQuery.toLowerCase()) ||
                  (seller.type && seller.type.toLowerCase().includes(sellerSearchQuery.toLowerCase()))
                )
              ]}
              keyExtractor={(item) => item.id?.toString() || 'none'}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={styles.modalItem}
                  onPress={() => {
                    

                    // Validate buyer/seller selection
                    if (item.id && item.id === formData.buyerId) {
                      setErrors(prev => ({
                        ...prev,
                        sellerId: 'Buyer and Seller cannot be the same person'
                      }));
                      setSellerModalVisible(false);
                      setSellerSearchQuery('');
                      return;
                    }

                    // Clear any existing error
                    if (errors.sellerId) {
                      setErrors(prev => ({
                        ...prev,
                        sellerId: null
                      }));
                    }

                    // Update local form data immediately
                    const newFormData = {
                      ...formData,
                      sellerId: item.id
                    };
                    setFormData(newFormData);

                    // Auto-submit to parent
                    if (onSubmit) {
                      onSubmit(newFormData);
                    }

                    // If this is first entry, also use global handler
                    if (isFirstEntry && onGlobalFieldChange) {
                      onGlobalFieldChange('sellerId', item.id);
                    } else {
                      // For non-first entries, this should not happen as they're disabled
                      
                    }

                    setSellerModalVisible(false);
                    setSellerSearchQuery('');
                  }}
                >
                  <Text style={styles.modalItemText}>
                    {item.name} {item.type && `(${item.type})`}
                  </Text>
                </TouchableOpacity>
              )}
            />
            <Button onPress={() => {
              setSellerModalVisible(false);
              setSellerSearchQuery('');
            }}>Cancel</Button>
          </View>
        </View>
      </Modal>

      {/* Tractor Selection Modal */}
      <Modal
        visible={tractorModalVisible}
        onRequestClose={() => {
          setTractorModalVisible(false);
          setTractorSearchQuery('');
        }}
        animationType="slide"
        transparent={true}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Select Tractor</Text>
            <TextInput
              label="Search tractors..."
              value={tractorSearchQuery}
              onChangeText={setTractorSearchQuery}
              style={styles.searchInput}
              mode="outlined"
              left={<TextInput.Icon icon="magnify" />}
            />
            <FlatList
              data={[
                { id: null, vehicle_number: 'None', brand: '' },
                ...tractors.filter(tractor =>
                  tractor.vehicle_number.toLowerCase().includes(tractorSearchQuery.toLowerCase()) ||
                  (tractor.brand && tractor.brand.toLowerCase().includes(tractorSearchQuery.toLowerCase())) ||
                  (tractor.driver_name && tractor.driver_name.toLowerCase().includes(tractorSearchQuery.toLowerCase()))
                )
              ]}
              keyExtractor={(item) => item.id?.toString() || 'none'}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={styles.modalItem}
                  onPress={() => {
                    

                    // Update form data with selected tractor
                    const newFormData = {
                      ...formData,
                      tractorId: item.id,
                      tractorNo: item.vehicle_number === 'None' ? '' : item.vehicle_number || '',
                      tractorWeight: item.tractor_weight_kg ? item.tractor_weight_kg.toString() : formData.tractorWeight
                    };

                    setFormData(newFormData);

                    // Auto-submit to parent
                    if (onSubmit) {
                      onSubmit(newFormData);
                    }

                    setTractorModalVisible(false);
                    setTractorSearchQuery('');
                  }}
                >
                  <Text style={styles.modalItemText}>
                    {item.vehicle_number} {item.brand && `(${item.brand})`}
                  </Text>
                </TouchableOpacity>
              )}
            />
            <Button onPress={() => {
              setTractorModalVisible(false);
              setTractorSearchQuery('');
            }}>Cancel</Button>
          </View>
        </View>
      </Modal>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    marginVertical: Spacing.sm,
    marginHorizontal: Spacing.md,
    borderRadius: 16,
    backgroundColor: Colors.surface,
    borderWidth: 0,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
    paddingHorizontal: Spacing.md,
    paddingTop: Spacing.md,
    backgroundColor: 'rgba(46, 125, 50, 0.02)',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    marginHorizontal: -Spacing.md,
    marginTop: -Spacing.md,
    paddingBottom: Spacing.md,
  },
  entryBadge: {
    backgroundColor: Colors.primary,
    borderRadius: 12,
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: Colors.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
  },
  entryBadgeText: {
    color: Colors.secondary, // Yellow text on green badge
    fontSize: 16,
    fontWeight: 'bold',
  },
  entryTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.primary,
    flex: 1,
    marginLeft: Spacing.md,
    letterSpacing: 0.3,
  },
  deleteButton: {
    margin: 0,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  expandButton: {
    margin: 0,
  },
  disabledInput: {
    opacity: 0.6,
  },
  touchableField: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  dropdownInput: {
    backgroundColor: '#F8F9FA',
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: Spacing.sm,
  },
  input: {
    marginBottom: Spacing.md,
    backgroundColor: '#F8F9FA',
  },
  inputHalf: {
    flex: 0.48,
  },
  inputFocused: {
    backgroundColor: '#FFFFFF',
  },
  errorText: {
    color: '#E53E3E',
    fontSize: 13,
    marginBottom: Spacing.xs,
    marginLeft: Spacing.sm,
    fontWeight: '500',
    letterSpacing: 0.2,
  },

  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.primary,
    marginBottom: Spacing.md,
    marginTop: Spacing.lg,
    paddingLeft: 4,
    letterSpacing: 0.5,
  },
  divider: {
    marginVertical: Spacing.lg,
    backgroundColor: '#F0F0F0',
    height: 1,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.lg,
  },
  modalContent: {
    backgroundColor: Colors.surface,
    borderRadius: 20,
    padding: Spacing.xl,
    width: '90%',
    maxHeight: '80%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  modalTitle: {
    fontSize: 22,
    fontWeight: '700',
    color: Colors.primary,
    marginBottom: Spacing.lg,
    textAlign: 'center',
    letterSpacing: 0.5,
  },
  searchInput: {
    marginBottom: Spacing.md,
    backgroundColor: '#F8F9FA',
  },
  modalItem: {
    padding: Spacing.lg,
    borderRadius: 12,
    marginVertical: 4,
    backgroundColor: '#F8F9FA',
    borderWidth: 1,
    borderColor: '#E9ECEF',
  },
  modalItemText: {
    fontSize: 16,
    color: Colors.text,
    fontWeight: '500',
  },
});

export default EntryForm;
