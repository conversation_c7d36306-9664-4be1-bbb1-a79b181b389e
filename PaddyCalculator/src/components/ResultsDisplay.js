import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Card, Text } from 'react-native-paper';
import { Colors, Spacing, Typography, BorderRadius } from '../constants/theme';
import { formatCurrency, formatWeight, formatYieldDisplay } from '../utils/calculations';

const ResultsDisplay = ({ entries, totals, showHeader = true }) => {
  if (!entries || entries.length === 0) {
    return (
      <Card style={styles.card}>
        <Card.Content style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No calculations yet</Text>
          <Text style={styles.emptySubtext}>Add entries above to see results</Text>
        </Card.Content>
      </Card>
    );
  }

  return (
    <Card style={styles.card}>
      <Card.Content>
        {showHeader && (
          <View style={styles.header}>
            <Text style={styles.title}>🌾 Calculation Results</Text>
          </View>
        )}
        


        {totals && (
          <View style={styles.summaryContainer}>
            <View style={styles.summaryHeader}>
              <Text style={styles.summaryTitle}>📊 Summary</Text>
            </View>

            <View style={styles.summaryContent}>
              <View style={styles.summaryGrid}>
                <View style={styles.summaryCard}>
                  <View style={styles.summaryIconContainer}>
                    <Text style={styles.summaryIcon}>📝</Text>
                  </View>
                  <Text style={styles.summaryLabel}>Total Entries</Text>
                  <Text style={styles.summaryValue}>{totals.entryCount}</Text>
                </View>

                <View style={styles.summaryCard}>
                  <View style={styles.summaryIconContainer}>
                    <Text style={styles.summaryIcon}>⚖️</Text>
                  </View>
                  <Text style={styles.summaryLabel}>Gross Weight</Text>
                  <Text style={styles.summaryValue}>{formatWeight(totals.totalGrossWeight)}</Text>
                </View>

                <View style={styles.summaryCard}>
                  <View style={styles.summaryIconContainer}>
                    <Text style={styles.summaryIcon}>📦</Text>
                  </View>
                  <Text style={styles.summaryLabel}>Adjusted Weight</Text>
                  <Text style={styles.summaryValue}>{formatWeight(totals.totalAdjustedWeight)}</Text>
                </View>

                <View style={[styles.summaryCard, styles.priceCard]}>
                  <View style={styles.summaryIconContainer}>
                    <Text style={styles.summaryIcon}>💰</Text>
                  </View>
                  <Text style={styles.summaryLabel}>Total Amount</Text>
                  <Text style={[styles.summaryValue, styles.priceValue]}>
                    {formatCurrency(totals.totalPrice)}
                  </Text>
                </View>
              </View>

              {totals.overallYieldRatio !== undefined && (
                <View style={styles.yieldCard}>
                  <View style={styles.yieldHeader}>
                    <Text style={styles.yieldIcon}>🌾</Text>
                    <Text style={styles.yieldTitle}>Overall Yield</Text>
                  </View>
                  <Text style={styles.yieldValue}>
                    {formatYieldDisplay(
                      totals.overallYieldRatio,
                      totals.overallBags,
                      totals.overallRemainingKg
                    )}
                  </Text>
                </View>
              )}
            </View>
          </View>
        )}
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    margin: Spacing.md,
    borderRadius: BorderRadius.lg,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: Spacing.xl,
  },
  emptyText: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.medium,
    color: Colors.textSecondary,
    marginBottom: Spacing.xs,
  },
  emptySubtext: {
    fontSize: Typography.fontSize.sm,
    color: Colors.textSecondary,
  },
  header: {
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },
  title: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.primary,
  },
  summaryContainer: {
    marginTop: Spacing.md,
  },
  summaryHeader: {
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },
  summaryTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.primary,
  },
  summaryContent: {
    backgroundColor: '#f8f9fa',
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  summaryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: Spacing.lg,
  },
  summaryCard: {
    width: '48%',
    backgroundColor: '#ffffff',
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    marginBottom: Spacing.md,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  priceCard: {
    backgroundColor: '#e8f5e8',
    borderColor: Colors.primary,
    borderWidth: 2,
  },
  summaryIconContainer: {
    marginBottom: Spacing.sm,
  },
  summaryIcon: {
    fontSize: 24,
  },
  summaryLabel: {
    fontSize: Typography.fontSize.sm,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: Spacing.xs,
    fontWeight: Typography.fontWeight.medium,
  },
  summaryValue: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.text,
    textAlign: 'center',
  },
  priceValue: {
    color: Colors.primary,
    fontSize: Typography.fontSize.xl,
  },
  yieldCard: {
    backgroundColor: '#fff3cd',
    borderRadius: BorderRadius.md,
    padding: Spacing.lg,
    borderLeftWidth: 4,
    borderLeftColor: '#ffc107',
  },
  yieldHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  yieldIcon: {
    fontSize: 20,
    marginRight: Spacing.sm,
  },
  yieldTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
    color: '#856404',
  },
  yieldValue: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
    color: '#856404',
    textAlign: 'center',
  },
});

export default ResultsDisplay;
