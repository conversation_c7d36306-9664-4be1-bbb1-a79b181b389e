import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Portal, Dialog, Text, Button, IconButton } from 'react-native-paper';
import { Colors, Spacing, Typography, BorderRadius } from '../constants/theme';

const ModernAlertDialog = ({
  visible,
  onDismiss,
  title,
  message,
  icon,
  iconColor = Colors.primary,
  buttonText = 'OK',
  buttonColor = Colors.primary,
  isError = false,
}) => {
  const handlePress = () => {
    onDismiss();
  };

  return (
    <Portal>
      <Dialog
        visible={visible}
        onDismiss={onDismiss}
        style={styles.dialog}
        theme={{ colors: { backdrop: 'rgba(0, 0, 0, 0.5)' } }}
      >
        <View style={styles.dialogContent}>
          {/* Icon Section */}
          {icon && (
            <View style={[
              styles.iconContainer,
              { backgroundColor: isError ? Colors.errorLowOpacity : Colors.primaryLowOpacity }
            ]}>
              <IconButton
                icon={icon}
                size={32}
                iconColor={isError ? Colors.error : iconColor}
                style={styles.dialogIcon}
              />
            </View>
          )}

          {/* Title */}
          <Text style={[
            styles.dialogTitle,
            { color: isError ? Colors.error : Colors.text }
          ]}>
            {title}
          </Text>

          {/* Message */}
          <Text style={styles.dialogMessage}>
            {message}
          </Text>

          {/* Action Button */}
          <Button
            mode="contained"
            onPress={handlePress}
            style={styles.button}
            contentStyle={styles.buttonContent}
            labelStyle={styles.buttonLabel}
            buttonColor={isError ? Colors.error : buttonColor}
          >
            {buttonText}
          </Button>
        </View>
      </Dialog>
    </Portal>
  );
};

const styles = StyleSheet.create({
  dialog: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.xl,
    marginHorizontal: Spacing.xl,
    elevation: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
  },
  dialogContent: {
    padding: Spacing.xl,
    alignItems: 'center',
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },
  dialogIcon: {
    margin: 0,
  },
  dialogTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
    textAlign: 'center',
    marginBottom: Spacing.md,
    lineHeight: 28,
  },
  dialogMessage: {
    fontSize: Typography.fontSize.md,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: Spacing.xl,
    lineHeight: 22,
    paddingHorizontal: Spacing.sm,
  },
  button: {
    borderRadius: BorderRadius.lg,
    minWidth: 120,
    elevation: 2,
    shadowColor: Colors.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  buttonContent: {
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.lg,
  },
  buttonLabel: {
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.textLight,
  },
});

export default ModernAlertDialog;
