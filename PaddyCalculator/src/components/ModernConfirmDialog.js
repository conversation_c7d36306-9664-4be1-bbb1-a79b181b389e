import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Portal, Dialog, Text, Button, IconButton } from 'react-native-paper';
import { Colors, Spacing, Typography, BorderRadius } from '../constants/theme';

const ModernConfirmDialog = ({
  visible,
  onDismiss,
  title,
  message,
  icon,
  iconColor = Colors.primary,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  onConfirm,
  onCancel,
  confirmButtonColor = Colors.primary,
  isDestructive = false,
}) => {
  const handleConfirm = () => {
    onConfirm?.();
    onDismiss();
  };

  const handleCancel = () => {
    onCancel?.();
    onDismiss();
  };

  return (
    <Portal>
      <Dialog
        visible={visible}
        onDismiss={onDismiss}
        style={styles.dialog}
        theme={{ colors: { backdrop: 'rgba(0, 0, 0, 0.5)' } }}
      >
        <View style={styles.dialogContent}>
          {/* Icon Section */}
          {icon && (
            <View style={[
              styles.iconContainer,
              { backgroundColor: isDestructive ? Colors.errorLowOpacity : Colors.primaryLowOpacity }
            ]}>
              <IconButton
                icon={icon}
                size={32}
                iconColor={isDestructive ? Colors.error : iconColor}
                style={styles.dialogIcon}
              />
            </View>
          )}

          {/* Title */}
          <Text style={[
            styles.dialogTitle,
            { color: isDestructive ? Colors.error : Colors.text }
          ]}>
            {title}
          </Text>

          {/* Message */}
          <Text style={styles.dialogMessage}>
            {message}
          </Text>

          {/* Action Buttons */}
          <View style={styles.buttonContainer}>
            <Button
              mode="outlined"
              onPress={handleCancel}
              style={[styles.button, styles.cancelButton]}
              contentStyle={styles.buttonContent}
              labelStyle={styles.cancelButtonLabel}
              textColor={Colors.textSecondary}
            >
              {cancelText}
            </Button>
            
            <Button
              mode="contained"
              onPress={handleConfirm}
              style={[styles.button, styles.confirmButton]}
              contentStyle={styles.buttonContent}
              labelStyle={styles.confirmButtonLabel}
              buttonColor={isDestructive ? Colors.error : confirmButtonColor}
            >
              {confirmText}
            </Button>
          </View>
        </View>
      </Dialog>
    </Portal>
  );
};

const styles = StyleSheet.create({
  dialog: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.xl,
    marginHorizontal: Spacing.xl,
    elevation: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
  },
  dialogContent: {
    padding: Spacing.xl,
    alignItems: 'center',
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },
  dialogIcon: {
    margin: 0,
  },
  dialogTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
    textAlign: 'center',
    marginBottom: Spacing.md,
    lineHeight: 28,
  },
  dialogMessage: {
    fontSize: Typography.fontSize.md,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: Spacing.xl,
    lineHeight: 22,
    paddingHorizontal: Spacing.sm,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: Spacing.md,
    width: '100%',
  },
  button: {
    flex: 1,
    borderRadius: BorderRadius.lg,
  },
  buttonContent: {
    paddingVertical: Spacing.sm,
  },
  cancelButton: {
    borderColor: Colors.border,
    backgroundColor: Colors.surface,
  },
  confirmButton: {
    elevation: 2,
    shadowColor: Colors.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  cancelButtonLabel: {
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.medium,
  },
  confirmButtonLabel: {
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.textLight,
  },
});

export default ModernConfirmDialog;
