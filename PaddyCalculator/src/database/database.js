import * as SQLite from 'expo-sqlite';

// Database configuration
const DATABASE_NAME = 'paddy_calculator.db';

// Initialize database
let db = null;

export const initDatabase = async () => {
  try {
    db = await SQLite.openDatabaseAsync(DATABASE_NAME);
    
    // Create tables
    await createTables();
    return db;
  } catch (error) {
    throw error;
  }
};

// Create database tables
const createTables = async () => {
  try {
    // First create farmers and tractors tables
    // Farmers table (for both buyers and sellers)
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS farmers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        type TEXT NOT NULL CHECK (type IN ('buyer', 'seller', 'both')),
        phone TEXT,
        address TEXT,
        village TEXT,
        district TEXT,
        state TEXT,
        pincode TEXT,
        aadhar_number TEXT,
        pan_number TEXT,
        bank_account TEXT,
        ifsc_code TEXT,
        notes TEXT,
        is_active INTEGER DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Tractors table
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS tractors (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        vehicle_number TEXT UNIQUE NOT NULL,
        brand TEXT,
        model TEXT,
        capacity_tons REAL,
        tractor_weight_kg REAL,
        driver_name TEXT,
        driver_phone TEXT,
        driver_license TEXT,
        owner_name TEXT,
        owner_phone TEXT,
        owner_address TEXT,
        registration_date DATE,
        insurance_expiry DATE,
        fitness_expiry DATE,
        notes TEXT,
        is_active INTEGER DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Check if paddy_entries table exists and needs migration
    try {
      const tableInfo = await db.getAllAsync("PRAGMA table_info(paddy_entries)");
      const hasNewColumns = tableInfo.some(col => col.name === 'buyer_id');

      if (!hasNewColumns && tableInfo.length > 0) {
        // Migration: Add new columns to existing table
        await db.execAsync(`ALTER TABLE paddy_entries ADD COLUMN buyer_id INTEGER`);
        await db.execAsync(`ALTER TABLE paddy_entries ADD COLUMN seller_id INTEGER`);
        await db.execAsync(`ALTER TABLE paddy_entries ADD COLUMN tractor_id INTEGER`);
      }
    } catch (migrationError) {
      // Migration not needed or table does not exist yet
    }

    // Check if tractors table needs tractor_weight_kg column
    try {
      const tractorTableInfo = await db.getAllAsync("PRAGMA table_info(tractors)");
      const hasTractorWeight = tractorTableInfo.some(col => col.name === 'tractor_weight_kg');

      if (!hasTractorWeight && tractorTableInfo.length > 0) {
        await db.execAsync(`ALTER TABLE tractors ADD COLUMN tractor_weight_kg REAL`);
      }
    } catch (migrationError) {
      // Tractor weight migration not needed or table does not exist yet
    }

    // Check if paddy_entries table needs generated_bill_no column
    try {
      const paddyTableInfo = await db.getAllAsync("PRAGMA table_info(paddy_entries)");
      const hasGeneratedBillNo = paddyTableInfo.some(col => col.name === 'generated_bill_no');

      if (!hasGeneratedBillNo && paddyTableInfo.length > 0) {
        await db.execAsync(`ALTER TABLE paddy_entries ADD COLUMN generated_bill_no TEXT`);
      }
    } catch (migrationError) {
      // Generated bill number migration not needed or table does not exist yet
    }

    // Paddy entries table (updated with foreign keys)
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS paddy_entries (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        bill_no TEXT NOT NULL,
        buyer_id INTEGER,
        seller_id INTEGER,
        tractor_id INTEGER,
        tractor_no TEXT NOT NULL,
        gross_weight REAL NOT NULL,
        tractor_weight REAL NOT NULL,
        rewash_percentage REAL NOT NULL,
        price_per_900 REAL NOT NULL,
        net_weight REAL NOT NULL,
        waste_weight REAL NOT NULL,
        adjusted_weight REAL NOT NULL,
        calculated_price REAL NOT NULL,
        yield_ratio INTEGER NOT NULL,
        num_bags INTEGER NOT NULL,
        remaining_kg REAL NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Calculation sessions table (for grouping entries)
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS calculation_sessions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        session_name TEXT,
        total_price REAL NOT NULL,
        total_entries INTEGER NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Junction table for session entries
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS session_entries (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        session_id INTEGER NOT NULL,
        entry_id INTEGER NOT NULL,
        FOREIGN KEY (session_id) REFERENCES calculation_sessions (id) ON DELETE CASCADE,
        FOREIGN KEY (entry_id) REFERENCES paddy_entries (id) ON DELETE CASCADE
      );
    `);

    // Farmers table (for both buyers and sellers)
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS farmers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        type TEXT NOT NULL CHECK (type IN ('buyer', 'seller', 'both')),
        phone TEXT,
        address TEXT,
        village TEXT,
        district TEXT,
        state TEXT,
        pincode TEXT,
        aadhar_number TEXT,
        pan_number TEXT,
        bank_account TEXT,
        ifsc_code TEXT,
        notes TEXT,
        is_active INTEGER DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Tractors table
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS tractors (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        vehicle_number TEXT UNIQUE NOT NULL,
        brand TEXT,
        model TEXT,
        capacity_tons REAL,
        driver_name TEXT,
        driver_phone TEXT,
        driver_license TEXT,
        owner_name TEXT,
        owner_phone TEXT,
        owner_address TEXT,
        registration_date DATE,
        insurance_expiry DATE,
        fitness_expiry DATE,
        notes TEXT,
        is_active INTEGER DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Settings table
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS app_settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        setting_key TEXT UNIQUE NOT NULL,
        setting_value TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Insert default settings
    await db.execAsync(`
      INSERT OR IGNORE INTO app_settings (setting_key, setting_value) VALUES
      ('default_rewash_percentage', '2.0'),
      ('company_name', 'CHEVURU SOFTWARE SOLUTIONS'),
      ('company_address', 'West Street, Kagithalapuru, Pincode 524405'),
      ('company_phone', '+91 9502953463'),
      ('company_email', '<EMAIL>'),
      ('company_website', 'www.chevurusoftwaresolutions.com');
    `);

    // Insert sample farmers for testing (force reload for demo)
    // Clear existing data first
    await db.execAsync('DELETE FROM paddy_entries');
    await db.execAsync('DELETE FROM farmers');
    await db.execAsync('DELETE FROM tractors');

    // Reset auto-increment counters
    await db.execAsync('DELETE FROM sqlite_sequence WHERE name IN ("farmers", "tractors", "paddy_entries")');

    // Insert sample farmers
      await db.execAsync(`
        INSERT INTO farmers (name, type, phone, village, address, district, state) VALUES
        -- Buyers
        ('Ravi Kumar', 'buyer', '+91 9876543210', 'Kagithalapuru', 'Main Street, Kagithalapuru', 'West Godavari', 'Andhra Pradesh'),
        ('Suresh Enterprises', 'buyer', '+91 9876543220', 'Eluru', 'Market Road, Eluru', 'West Godavari', 'Andhra Pradesh'),
        ('Venkat Rice Mills', 'buyer', '+91 9876543230', 'Bhimavaram', 'Industrial Area, Bhimavaram', 'West Godavari', 'Andhra Pradesh'),
        ('Krishna Traders', 'buyer', '+91 9876543240', 'Vijayawada', 'Commercial Complex, Vijayawada', 'Krishna', 'Andhra Pradesh'),

        -- Sellers
        ('Sita Devi', 'seller', '+91 9876543211', 'Rajahmundry', 'Village Road, Rajahmundry', 'East Godavari', 'Andhra Pradesh'),
        ('Lakshmi Prasad', 'seller', '+91 9876543213', 'Vijayawada', 'Farm House, Vijayawada', 'Krishna', 'Andhra Pradesh'),
        ('Ramesh Farmer', 'seller', '+91 9876543250', 'Tanuku', 'Agricultural Land, Tanuku', 'West Godavari', 'Andhra Pradesh'),
        ('Gopi Krishna', 'seller', '+91 9876543260', 'Narsapuram', 'Paddy Fields, Narsapuram', 'West Godavari', 'Andhra Pradesh'),
        ('Anjali Farms', 'seller', '+91 9876543270', 'Palakollu', 'Organic Farm, Palakollu', 'West Godavari', 'Andhra Pradesh'),
        ('Subba Rao', 'seller', '+91 9876543280', 'Bhimadole', 'Village Center, Bhimadole', 'West Godavari', 'Andhra Pradesh'),

        -- Both (Buyer and Seller)
        ('Mohan Reddy', 'both', '+91 9876543212', 'Eluru', 'Business Center, Eluru', 'West Godavari', 'Andhra Pradesh'),
        ('Padma Agro', 'both', '+91 9876543290', 'Jangareddygudem', 'Agro Complex, Jangareddygudem', 'West Godavari', 'Andhra Pradesh');
      `);

    // Insert sample tractors for testing
      await db.execAsync(`
        INSERT INTO tractors (vehicle_number, brand, model, tractor_weight_kg, capacity_tons, driver_name, driver_phone, owner_name, owner_phone) VALUES
        ('AP 01 AB 1234', 'Mahindra', '575 DI', 2500, 10.5, 'Ramesh Kumar', '+91 9876543220', 'Ravi Transport', '+91 9876543320'),
        ('AP 02 CD 5678', 'John Deere', '5050E', 2800, 12.0, 'Suresh Reddy', '+91 9876543221', 'Suresh Logistics', '+91 9876543321'),
        ('AP 03 EF 9012', 'Sonalika', 'DI 745', 2300, 9.5, 'Ganesh Babu', '+91 9876543222', 'Ganesh Transport', '+91 9876543322'),
        ('AP 04 GH 3456', 'Tafe', '9500 DI', 2600, 11.0, 'Venkat Rao', '+91 9876543223', 'Venkat Carriers', '+91 9876543323'),
        ('AP 05 IJ 7890', 'Mahindra', '605 DI', 2400, 10.0, 'Krishna Murthy', '+91 9876543224', 'Krishna Transport', '+91 9876543324'),
        ('AP 06 KL 2468', 'Eicher', '485', 2200, 8.5, 'Srinivas', '+91 9876543225', 'Sri Transport', '+91 9876543325'),
        ('AP 07 MN 1357', 'New Holland', '3630', 2700, 11.5, 'Rajesh', '+91 9876543226', 'Rajesh Logistics', '+91 9876543326'),
        ('AP 08 OP 9753', 'Kubota', 'MU5501', 2100, 7.5, 'Naresh', '+91 9876543227', 'Naresh Transport', '+91 9876543327');
      `);

    // Insert sample paddy entries for testing
      await db.execAsync(`
        INSERT INTO paddy_entries (
          bill_no, buyer_id, seller_id, tractor_id, tractor_no,
          gross_weight, tractor_weight, rewash_percentage, price_per_900,
          net_weight, waste_weight, adjusted_weight, calculated_price,
          yield_ratio, num_bags, remaining_kg, created_at
        ) VALUES
        -- Ravi Kumar (Buyer) transactions
        ('BILL001', 1, 2, 1, 'AP 01 AB 1234', 5000, 2500, 2.0, 2800, 2500, 50, 2450, 7622.22, 25, 2, 350, '2024-01-15 10:30:00'),
        ('BILL002', 1, 3, 2, 'AP 02 CD 5678', 4500, 2800, 2.5, 2850, 1700, 42.5, 1657.5, 5262.50, 18, 1, 557.5, '2024-01-16 11:15:00'),
        ('BILL003', 1, 4, 3, 'AP 03 EF 9012', 6200, 2300, 1.8, 2900, 3900, 70.2, 3829.8, 12344.27, 42, 4, 229.8, '2024-01-17 09:45:00'),

        -- Suresh Enterprises (Buyer) transactions
        ('BILL004', 2, 5, 4, 'AP 04 GH 3456', 5500, 2600, 2.2, 2750, 2900, 63.8, 2836.2, 8681.28, 31, 3, 136.2, '2024-01-18 14:20:00'),
        ('BILL005', 2, 6, 5, 'AP 05 IJ 7890', 4800, 2400, 2.0, 2820, 2400, 48, 2352, 7372.80, 26, 2, 252, '2024-01-19 08:30:00'),
        ('BILL006', 2, 7, 6, 'AP 06 KL 2468', 3900, 2200, 1.5, 2880, 1700, 25.5, 1674.5, 5367.20, 18, 1, 574.5, '2024-01-20 16:10:00'),

        -- Venkat Rice Mills (Buyer) transactions
        ('BILL007', 3, 8, 7, 'AP 07 MN 1357', 7200, 2700, 2.3, 2950, 4500, 103.5, 4396.5, 14411.17, 48, 4, 796.5, '2024-01-21 12:45:00'),
        ('BILL008', 3, 2, 8, 'AP 08 OP 9753', 5800, 2100, 1.9, 2870, 3700, 70.3, 3629.7, 11628.23, 40, 4, 29.7, '2024-01-22 10:15:00'),
        ('BILL009', 3, 9, 1, 'AP 01 AB 1234', 4200, 2500, 2.1, 2800, 1700, 35.7, 1664.3, 5179.73, 18, 1, 564.3, '2024-01-23 15:30:00'),

        -- Krishna Traders (Buyer) transactions
        ('BILL010', 4, 10, 2, 'AP 02 CD 5678', 6500, 2800, 2.4, 2920, 3700, 88.8, 3611.2, 11702.72, 40, 4, 11.2, '2024-01-24 09:20:00'),
        ('BILL011', 4, 3, 3, 'AP 03 EF 9012', 5200, 2300, 2.0, 2850, 2900, 58, 2842, 9048.67, 31, 3, 142, '2024-01-25 13:40:00'),
        ('BILL012', 4, 11, 4, 'AP 04 GH 3456', 4600, 2600, 1.7, 2780, 2000, 34, 1966, 6074.22, 21, 2, 66, '2024-01-26 11:55:00'),

        -- Mohan Reddy (Both) as buyer transactions
        ('BILL013', 11, 5, 5, 'AP 05 IJ 7890', 3800, 2400, 2.2, 2760, 1400, 30.8, 1369.2, 4215.95, 15, 1, 269.2, '2024-01-27 14:25:00'),
        ('BILL014', 11, 6, 6, 'AP 06 KL 2468', 5100, 2200, 1.9, 2890, 2900, 55.1, 2844.9, 9164.87, 31, 3, 144.9, '2024-01-28 10:40:00'),

        -- Padma Agro (Both) as buyer transactions
        ('BILL015', 12, 7, 7, 'AP 07 MN 1357', 6800, 2700, 2.5, 3000, 4100, 102.5, 3997.5, 13325.00, 44, 4, 397.5, '2024-01-29 12:15:00');
      `);

    // Saved Bills table (for permanently storing grouped bills)
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS saved_bills (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        bill_no TEXT NOT NULL UNIQUE,
        generated_bill_no TEXT,
        buyer_id INTEGER,
        seller_id INTEGER,
        total_amount REAL NOT NULL,
        total_weight REAL NOT NULL,
        entry_count INTEGER NOT NULL,
        bill_data TEXT NOT NULL, -- JSON string of all entries
        notes TEXT,
        is_active INTEGER DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (buyer_id) REFERENCES farmers (id),
        FOREIGN KEY (seller_id) REFERENCES farmers (id)
      );
    `);

  } catch (error) {
    throw error;
  }
};

// Get database instance
export const getDatabase = () => {
  if (!db) {
    throw new Error('Database not initialized. Call initDatabase() first.');
  }
  return db;
};

// Paddy Entry CRUD operations
export const insertPaddyEntry = async (entryData) => {
  try {
    const database = getDatabase();
    const result = await database.runAsync(
      `INSERT INTO paddy_entries (
        bill_no, buyer_id, seller_id, tractor_id, tractor_no, gross_weight, tractor_weight,
        rewash_percentage, price_per_900, net_weight, waste_weight, adjusted_weight,
        calculated_price, yield_ratio, num_bags, remaining_kg, generated_bill_no
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        entryData.billNo,
        entryData.buyerId || null,
        entryData.sellerId || null,
        entryData.tractorId || null,
        entryData.tractorNo,
        entryData.grossWeight,
        entryData.tractorWeight,
        entryData.rewashPercentage,
        entryData.pricePer900,
        entryData.netWeight,
        entryData.wasteWeight,
        entryData.adjustedWeight,
        entryData.calculatedPrice,
        entryData.yieldRatio,
        entryData.numBags,
        entryData.remainingKg,
        entryData.generatedBillNo || null
      ]
    );
    return result.lastInsertRowId;
  } catch (error) {
    throw error;
  }
};

export const getAllPaddyEntries = async () => {
  try {
    const database = getDatabase();
    const result = await database.getAllAsync('SELECT * FROM paddy_entries ORDER BY created_at DESC');
    return result;
  } catch (error) {
    throw error;
  }
};

export const getPaddyEntryById = async (id) => {
  try {
    const database = getDatabase();
    const result = await database.getFirstAsync('SELECT * FROM paddy_entries WHERE id = ?', [id]);
    return result;
  } catch (error) {
    
    throw error;
  }
};

export const updatePaddyEntry = async (id, entryData) => {
  try {
    const database = getDatabase();
    await database.runAsync(
      `UPDATE paddy_entries SET 
        bill_no = ?, tractor_no = ?, gross_weight = ?, tractor_weight = ?, 
        rewash_percentage = ?, price_per_900 = ?, net_weight = ?, waste_weight = ?, 
        adjusted_weight = ?, calculated_price = ?, yield_ratio = ?, num_bags = ?, 
        remaining_kg = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?`,
      [
        entryData.billNo, entryData.tractorNo, entryData.grossWeight, entryData.tractorWeight,
        entryData.rewashPercentage, entryData.pricePer900, entryData.netWeight, entryData.wasteWeight,
        entryData.adjustedWeight, entryData.calculatedPrice, entryData.yieldRatio, entryData.numBags,
        entryData.remainingKg, id
      ]
    );
  } catch (error) {
    
    throw error;
  }
};

export const deletePaddyEntry = async (id) => {
  try {
    const database = getDatabase();
    await database.runAsync('DELETE FROM paddy_entries WHERE id = ?', [id]);
  } catch (error) {
    
    throw error;
  }
};

// Session CRUD operations
export const createCalculationSession = async (sessionName, entryIds, totalPrice) => {
  try {
    const database = getDatabase();
    
    // Insert session
    const sessionResult = await database.runAsync(
      'INSERT INTO calculation_sessions (session_name, total_price, total_entries) VALUES (?, ?, ?)',
      [sessionName, totalPrice, entryIds.length]
    );
    
    const sessionId = sessionResult.lastInsertRowId;
    
    // Insert session entries
    for (const entryId of entryIds) {
      await database.runAsync(
        'INSERT INTO session_entries (session_id, entry_id) VALUES (?, ?)',
        [sessionId, entryId]
      );
    }
    
    return sessionId;
  } catch (error) {
    
    throw error;
  }
};

export const getAllSessions = async () => {
  try {
    const database = getDatabase();
    const result = await database.getAllAsync('SELECT * FROM calculation_sessions ORDER BY created_at DESC');
    return result;
  } catch (error) {
    
    throw error;
  }
};

export const getSessionWithEntries = async (sessionId) => {
  try {
    const database = getDatabase();
    
    // Get session details
    const session = await database.getFirstAsync('SELECT * FROM calculation_sessions WHERE id = ?', [sessionId]);
    
    // Get session entries
    const entries = await database.getAllAsync(`
      SELECT pe.* FROM paddy_entries pe
      JOIN session_entries se ON pe.id = se.entry_id
      WHERE se.session_id = ?
      ORDER BY pe.created_at
    `, [sessionId]);
    
    return { ...session, entries };
  } catch (error) {
    
    throw error;
  }
};

// Settings operations
export const getSetting = async (key) => {
  try {
    const database = getDatabase();
    const result = await database.getFirstAsync('SELECT setting_value FROM app_settings WHERE setting_key = ?', [key]);
    return result?.setting_value || null;
  } catch (error) {
    
    throw error;
  }
};

export const setSetting = async (key, value) => {
  try {
    const database = getDatabase();
    await database.runAsync(
      'INSERT OR REPLACE INTO app_settings (setting_key, setting_value, updated_at) VALUES (?, ?, CURRENT_TIMESTAMP)',
      [key, value]
    );
  } catch (error) {
    
    throw error;
  }
};

// Farmer CRUD operations
export const insertFarmer = async (farmerData) => {
  try {
    const database = getDatabase();
    const result = await database.runAsync(
      `INSERT INTO farmers (
        name, type, phone, address, village, district, state, pincode,
        aadhar_number, pan_number, bank_account, ifsc_code, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        farmerData.name,
        farmerData.type,
        farmerData.phone || null,
        farmerData.address || null,
        farmerData.village || null,
        farmerData.district || null,
        farmerData.state || null,
        farmerData.pincode || null,
        farmerData.aadharNumber || null,
        farmerData.panNumber || null,
        farmerData.bankAccount || null,
        farmerData.ifscCode || null,
        farmerData.notes || null
      ]
    );
    return result.lastInsertRowId;
  } catch (error) {
    
    throw error;
  }
};

export const getAllFarmers = async (type = null) => {
  try {
    const database = getDatabase();
    let query = 'SELECT * FROM farmers WHERE is_active = 1';
    let params = [];

    if (type) {
      query += ' AND (type = ? OR type = "both")';
      params.push(type);
    }

    query += ' ORDER BY name';
    const result = await database.getAllAsync(query, params);
    return result;
  } catch (error) {
    
    throw error;
  }
};

export const getFarmerById = async (id) => {
  try {
    const database = getDatabase();
    const result = await database.getFirstAsync('SELECT * FROM farmers WHERE id = ?', [id]);
    return result;
  } catch (error) {
    
    throw error;
  }
};

export const updateFarmer = async (id, farmerData) => {
  try {
    const database = getDatabase();
    await database.runAsync(
      `UPDATE farmers SET
        name = ?, type = ?, phone = ?, address = ?, village = ?, district = ?,
        state = ?, pincode = ?, aadhar_number = ?, pan_number = ?, bank_account = ?,
        ifsc_code = ?, notes = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?`,
      [
        farmerData.name, farmerData.type, farmerData.phone, farmerData.address,
        farmerData.village, farmerData.district, farmerData.state, farmerData.pincode,
        farmerData.aadharNumber, farmerData.panNumber, farmerData.bankAccount,
        farmerData.ifscCode, farmerData.notes, id
      ]
    );
  } catch (error) {
    
    throw error;
  }
};

export const deleteFarmer = async (id) => {
  try {
    const database = getDatabase();
    await database.runAsync('UPDATE farmers SET is_active = 0 WHERE id = ?', [id]);
  } catch (error) {
    
    throw error;
  }
};

// Tractor CRUD operations
export const insertTractor = async (tractorData) => {
  try {
    const database = getDatabase();
    const result = await database.runAsync(
      `INSERT INTO tractors (
        vehicle_number, brand, model, capacity_tons, tractor_weight_kg, driver_name, driver_phone,
        driver_license, owner_name, owner_phone, owner_address, registration_date,
        insurance_expiry, fitness_expiry, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        tractorData.vehicleNumber,
        tractorData.brand || null,
        tractorData.model || null,
        tractorData.capacityTons || null,
        tractorData.tractorWeightKg || null,
        tractorData.driverName || null,
        tractorData.driverPhone || null,
        tractorData.driverLicense || null,
        tractorData.ownerName || null,
        tractorData.ownerPhone || null,
        tractorData.ownerAddress || null,
        tractorData.registrationDate || null,
        tractorData.insuranceExpiry || null,
        tractorData.fitnessExpiry || null,
        tractorData.notes || null
      ]
    );
    return result.lastInsertRowId;
  } catch (error) {
    
    throw error;
  }
};

export const getAllTractors = async () => {
  try {
    const database = getDatabase();
    const result = await database.getAllAsync(
      'SELECT * FROM tractors WHERE is_active = 1 ORDER BY vehicle_number'
    );
    return result;
  } catch (error) {
    
    throw error;
  }
};

export const getTractorById = async (id) => {
  try {
    const database = getDatabase();
    const result = await database.getFirstAsync('SELECT * FROM tractors WHERE id = ?', [id]);
    return result;
  } catch (error) {
    
    throw error;
  }
};

export const updateTractor = async (id, tractorData) => {
  try {
    const database = getDatabase();
    await database.runAsync(
      `UPDATE tractors SET
        vehicle_number = ?, brand = ?, model = ?, capacity_tons = ?, tractor_weight_kg = ?, driver_name = ?,
        driver_phone = ?, driver_license = ?, owner_name = ?, owner_phone = ?,
        owner_address = ?, registration_date = ?, insurance_expiry = ?, fitness_expiry = ?,
        notes = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?`,
      [
        tractorData.vehicleNumber, tractorData.brand, tractorData.model, tractorData.capacityTons,
        tractorData.tractorWeightKg, tractorData.driverName, tractorData.driverPhone, tractorData.driverLicense,
        tractorData.ownerName, tractorData.ownerPhone, tractorData.ownerAddress,
        tractorData.registrationDate, tractorData.insuranceExpiry, tractorData.fitnessExpiry,
        tractorData.notes, id
      ]
    );
  } catch (error) {
    
    throw error;
  }
};

export const deleteTractor = async (id) => {
  try {
    const database = getDatabase();
    await database.runAsync('UPDATE tractors SET is_active = 0 WHERE id = ?', [id]);
  } catch (error) {
    
    throw error;
  }
};

// ==================== SAVED BILLS FUNCTIONS ====================

export const saveBill = async (billData) => {
  try {
    const database = getDatabase();

    // Prepare the bill data
    const {
      bill_no,
      generated_bill_no,
      buyer_id,
      seller_id,
      total_amount,
      total_weight,
      entry_count,
      entries,
      notes = ''
    } = billData;

    // Check if bill already exists
    const existingBill = await database.getFirstAsync(
      `SELECT id FROM saved_bills WHERE bill_no = ? AND is_active = 1`,
      [bill_no]
    );

    if (existingBill) {
      throw new Error(`Bill ${bill_no} already exists. Cannot save duplicate bill.`);
    }

    // Convert entries to JSON string
    const billDataJson = JSON.stringify(entries);

    const result = await database.runAsync(
      `INSERT INTO saved_bills (
        bill_no, generated_bill_no, buyer_id, seller_id,
        total_amount, total_weight, entry_count, bill_data, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        bill_no,
        generated_bill_no,
        buyer_id,
        seller_id,
        total_amount,
        total_weight,
        entry_count,
        billDataJson,
        notes
      ]
    );

    return result.lastInsertRowId;
  } catch (error) {
    
    throw error;
  }
};

export const getSavedBills = async () => {
  try {
    const database = getDatabase();
    const bills = await database.getAllAsync(`
      SELECT sb.*,
             b.name as buyer_name,
             s.name as seller_name
      FROM saved_bills sb
      LEFT JOIN farmers b ON sb.buyer_id = b.id
      LEFT JOIN farmers s ON sb.seller_id = s.id
      WHERE sb.is_active = 1
      ORDER BY sb.created_at DESC
    `);

    // Parse the bill_data JSON for each bill
    return bills.map(bill => ({
      ...bill,
      entries: JSON.parse(bill.bill_data)
    }));
  } catch (error) {
    
    throw error;
  }
};

export const getSavedBillById = async (id) => {
  try {
    const database = getDatabase();
    const bill = await database.getFirstAsync(`
      SELECT sb.*,
             b.name as buyer_name,
             s.name as seller_name
      FROM saved_bills sb
      LEFT JOIN farmers b ON sb.buyer_id = b.id
      LEFT JOIN farmers s ON sb.seller_id = s.id
      WHERE sb.id = ? AND sb.is_active = 1
    `, [id]);

    if (bill) {
      bill.entries = JSON.parse(bill.bill_data);
    }

    return bill;
  } catch (error) {
    
    throw error;
  }
};

export const updateSavedBill = async (id, billData) => {
  try {
    const database = getDatabase();

    const {
      bill_no,
      generated_bill_no,
      buyer_id,
      seller_id,
      total_amount,
      total_weight,
      entry_count,
      entries,
      notes = ''
    } = billData;

    const billDataJson = JSON.stringify(entries);

    await database.runAsync(
      `UPDATE saved_bills SET
        bill_no = ?, generated_bill_no = ?, buyer_id = ?, seller_id = ?,
        total_amount = ?, total_weight = ?, entry_count = ?, bill_data = ?,
        notes = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?`,
      [
        bill_no,
        generated_bill_no,
        buyer_id,
        seller_id,
        total_amount,
        total_weight,
        entry_count,
        billDataJson,
        notes,
        id
      ]
    );
  } catch (error) {
    
    throw error;
  }
};

export const deleteSavedBill = async (id) => {
  try {
    const database = getDatabase();
    await database.runAsync('UPDATE saved_bills SET is_active = 0 WHERE id = ?', [id]);
  } catch (error) {
    
    throw error;
  }
};
