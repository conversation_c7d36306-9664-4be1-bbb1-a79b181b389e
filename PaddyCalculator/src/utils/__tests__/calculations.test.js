/**
 * Tests for calculation utilities
 */

import {
  calculatePaddyEntry,
  calculateTotals,
  validateEntryInput,
  formatCurrency,
  formatWeight,
  formatYieldDisplay
} from '../calculations';

describe('Paddy Calculations', () => {
  describe('calculatePaddyEntry', () => {
    test('should calculate paddy entry correctly', () => {
      const input = {
        billNo: 'B001',
        tractorNo: 'T001',
        grossWeight: 1000,
        tractorWeight: 100,
        rewashPercentage: 2.0,
        pricePer900: 2000
      };

      const result = calculatePaddyEntry(input);

      expect(result.netWeight).toBe(900);
      expect(result.wasteWeight).toBe(2); // Math.ceil((900/1000) * 2.0)
      expect(result.adjustedWeight).toBe(898);
      expect(result.calculatedPrice).toBeCloseTo(1995.56, 2); // (898/900) * 2000
      expect(result.yieldRatio).toBe(0); // Math.floor(898/900)
      expect(result.numBags).toBe(19); // Math.floor(898/45)
      expect(result.remainingKg).toBe(43); // 898 % 45
    });

    test('should handle larger quantities correctly', () => {
      const input = {
        billNo: 'B002',
        tractorNo: 'T002',
        grossWeight: 2000,
        tractorWeight: 200,
        rewashPercentage: 2.5,
        pricePer900: 2100
      };

      const result = calculatePaddyEntry(input);

      expect(result.netWeight).toBe(1800);
      expect(result.wasteWeight).toBe(5); // Math.ceil((1800/1000) * 2.5)
      expect(result.adjustedWeight).toBe(1795);
      expect(result.calculatedPrice).toBeCloseTo(4188.89, 2); // (1795/900) * 2100
      expect(result.yieldRatio).toBe(1); // Math.floor(1795/900)
      expect(result.numBags).toBe(19); // Math.floor(895/45)
      expect(result.remainingKg).toBe(40); // 895 % 45
    });

    test('should throw error for invalid inputs', () => {
      const invalidInput = {
        billNo: '',
        tractorNo: 'T001',
        grossWeight: 1000,
        tractorWeight: 100,
        rewashPercentage: 2.0,
        pricePer900: 2000
      };

      expect(() => calculatePaddyEntry(invalidInput)).toThrow();
    });

    test('should throw error when tractor weight >= gross weight', () => {
      const invalidInput = {
        billNo: 'B001',
        tractorNo: 'T001',
        grossWeight: 1000,
        tractorWeight: 1000,
        rewashPercentage: 2.0,
        pricePer900: 2000
      };

      expect(() => calculatePaddyEntry(invalidInput)).toThrow();
    });
  });

  describe('calculateTotals', () => {
    test('should calculate totals for multiple entries', () => {
      const entries = [
        {
          grossWeight: 1000,
          tractorWeight: 100,
          netWeight: 900,
          wasteWeight: 2,
          adjustedWeight: 898,
          calculatedPrice: 1995.56,
          yieldRatio: 0,
          numBags: 19,
          remainingKg: 43
        },
        {
          grossWeight: 1500,
          tractorWeight: 150,
          netWeight: 1350,
          wasteWeight: 3,
          adjustedWeight: 1347,
          calculatedPrice: 2993.33,
          yieldRatio: 1,
          numBags: 9,
          remainingKg: 42
        }
      ];

      const totals = calculateTotals(entries);

      expect(totals.totalGrossWeight).toBe(2500);
      expect(totals.totalTractorWeight).toBe(250);
      expect(totals.totalNetWeight).toBe(2250);
      expect(totals.totalAdjustedWeight).toBe(2245);
      expect(totals.totalPrice).toBeCloseTo(4988.89, 2);
      expect(totals.entryCount).toBe(2);
      expect(totals.overallYieldRatio).toBe(2); // Math.floor(2245/900)
    });

    test('should return zero totals for empty array', () => {
      const totals = calculateTotals([]);

      expect(totals.totalGrossWeight).toBe(0);
      expect(totals.totalPrice).toBe(0);
      expect(totals.entryCount).toBe(0);
    });
  });

  describe('validateEntryInput', () => {
    test('should validate correct input', () => {
      const input = {
        billNo: 'B001',
        tractorNo: 'T001',
        grossWeight: 1000,
        tractorWeight: 100,
        rewashPercentage: 2.0,
        pricePer900: 2000
      };

      const validation = validateEntryInput(input);

      expect(validation.isValid).toBe(true);
      expect(Object.keys(validation.errors)).toHaveLength(0);
    });

    test('should detect missing required fields', () => {
      const input = {
        billNo: '',
        tractorNo: '',
        grossWeight: 0,
        tractorWeight: -1,
        rewashPercentage: -1,
        pricePer900: 0
      };

      const validation = validateEntryInput(input);

      expect(validation.isValid).toBe(false);
      expect(validation.errors.billNo).toBeDefined();
      expect(validation.errors.tractorNo).toBeDefined();
      expect(validation.errors.grossWeight).toBeDefined();
      expect(validation.errors.tractorWeight).toBeDefined();
      expect(validation.errors.rewashPercentage).toBeDefined();
      expect(validation.errors.pricePer900).toBeDefined();
    });

    test('should detect tractor weight >= gross weight', () => {
      const input = {
        billNo: 'B001',
        tractorNo: 'T001',
        grossWeight: 1000,
        tractorWeight: 1000,
        rewashPercentage: 2.0,
        pricePer900: 2000
      };

      const validation = validateEntryInput(input);

      expect(validation.isValid).toBe(false);
      expect(validation.errors.tractorWeight).toContain('must be less than');
    });
  });

  describe('formatCurrency', () => {
    test('should format currency correctly', () => {
      expect(formatCurrency(1000)).toContain('1,000');
      expect(formatCurrency(1234.56)).toContain('1,234.56');
    });
  });

  describe('formatWeight', () => {
    test('should format weight correctly', () => {
      expect(formatWeight(1000)).toBe('1000.00 kg');
      expect(formatWeight(1234.567)).toBe('1234.57 kg');
    });
  });

  describe('formatYieldDisplay', () => {
    test('should format yield display correctly', () => {
      expect(formatYieldDisplay(1, 1, 10.5)).toBe('1 Putti 1 Basta 10.50 Kg\'s');
      expect(formatYieldDisplay(2, 3, 15.25)).toBe('2 Puttla 3 Bastala 15.25 Kg\'s');
      expect(formatYieldDisplay(0, 0, 5.0)).toBe('0 Puttla 0 Bastala 5.00 Kg\'s');
    });
  });
});
