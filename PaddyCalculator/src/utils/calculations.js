/**
 * Paddy Calculator - Core calculation logic
 * Ported from Python implementation
 */

/**
 * Calculate paddy entry values based on input parameters
 * @param {Object} input - Input parameters
 * @param {string} input.billNo - Bill number
 * @param {string} input.tractorNo - Tractor number
 * @param {number} input.grossWeight - Gross weight in kg
 * @param {number} input.tractorWeight - Tractor weight in kg
 * @param {number} input.rewashPercentage - Rewash percentage
 * @param {number} input.pricePer900 - Price per 900kg
 * @returns {Object} Calculated values
 */
export const calculatePaddyEntry = (input) => {
  try {
    const {
      billNo,
      buyerId,
      sellerId,
      tractorId,
      tractorNo,
      grossWeight,
      tractorWeight,
      rewashPercentage,
      pricePer900
    } = input;

    // Convert string inputs to numbers for calculations
    const grossWeightNum = parseFloat(grossWeight);
    const tractorWeightNum = parseFloat(tractorWeight);
    const rewashPercentageNum = parseFloat(rewashPercentage);
    const pricePer900Num = parseFloat(pricePer900);

    // Validate inputs
    if (!billNo || !tractorNo) {
      throw new Error('Bill number and tractor number are required');
    }

    if (grossWeightNum <= 0 || tractorWeightNum < 0 || rewashPercentageNum < 0 || pricePer900Num <= 0) {
      throw new Error('Invalid numeric values provided');
    }

    if (tractorWeightNum >= grossWeightNum) {
      throw new Error('Tractor weight cannot be greater than or equal to gross weight');
    }

    // Core calculations (matching Python logic)
    const netWeight = grossWeightNum - tractorWeightNum;
    const wasteWeight = Math.ceil((netWeight / 1000) * rewashPercentageNum);
    const adjustedWeight = netWeight - wasteWeight;
    const calculatedPrice = (adjustedWeight / 900) * pricePer900Num;

    // Yield calculations
    const yieldRatio = Math.floor(adjustedWeight / 900);
    const paddyWeight = adjustedWeight - (yieldRatio * 900);
    const numBags = Math.floor(paddyWeight / 45);
    const remainingKg = paddyWeight % 45;

    return {
      // Preserve all input fields including IDs
      billNo,
      buyerId,
      sellerId,
      tractorId,
      tractorNo,
      grossWeight: grossWeightNum,
      tractorWeight: tractorWeightNum,
      rewashPercentage: rewashPercentageNum,
      pricePer900: pricePer900Num,
      // Calculated fields
      netWeight,
      wasteWeight,
      adjustedWeight,
      calculatedPrice,
      yieldRatio,
      paddyWeight,
      numBags,
      remainingKg
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Calculate totals for multiple entries
 * @param {Array} entries - Array of calculated entries
 * @returns {Object} Total calculations
 */
export const calculateTotals = (entries) => {
  try {
    if (!Array.isArray(entries) || entries.length === 0) {
      return {
        totalGrossWeight: 0,
        totalTractorWeight: 0,
        totalNetWeight: 0,
        totalWasteWeight: 0,
        totalAdjustedWeight: 0,
        totalPrice: 0,
        totalYieldRatio: 0,
        totalBags: 0,
        totalRemainingKg: 0,
        entryCount: 0
      };
    }

    const totals = entries.reduce((acc, entry) => {
      acc.totalGrossWeight += entry.grossWeight || 0;
      acc.totalTractorWeight += entry.tractorWeight || 0;
      acc.totalNetWeight += entry.netWeight || 0;
      acc.totalWasteWeight += entry.wasteWeight || 0;
      acc.totalAdjustedWeight += entry.adjustedWeight || 0;
      acc.totalPrice += entry.calculatedPrice || 0;
      acc.totalYieldRatio += entry.yieldRatio || 0;
      acc.totalBags += entry.numBags || 0;
      acc.totalRemainingKg += entry.remainingKg || 0;
      return acc;
    }, {
      totalGrossWeight: 0,
      totalTractorWeight: 0,
      totalNetWeight: 0,
      totalWasteWeight: 0,
      totalAdjustedWeight: 0,
      totalPrice: 0,
      totalYieldRatio: 0,
      totalBags: 0,
      totalRemainingKg: 0
    });

    // Calculate overall yield summary
    const overallYieldRatio = Math.floor(totals.totalAdjustedWeight / 900);
    const overallPaddyWeight = totals.totalAdjustedWeight - (overallYieldRatio * 900);
    const overallBags = Math.floor(overallPaddyWeight / 45);
    const overallRemainingKg = overallPaddyWeight % 45;

    return {
      ...totals,
      entryCount: entries.length,
      overallYieldRatio,
      overallPaddyWeight,
      overallBags,
      overallRemainingKg
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Format yield display text (matching Python logic)
 * @param {number} yieldRatio - Yield ratio
 * @param {number} numBags - Number of bags
 * @param {number} remainingKg - Remaining kg
 * @returns {string} Formatted yield text
 */
export const formatYieldDisplay = (yieldRatio, numBags, remainingKg) => {
  const safeYieldRatio = yieldRatio || 0;
  const safeNumBags = numBags || 0;
  const safeRemainingKg = remainingKg || 0;

  const puttiText = safeYieldRatio === 1 ? 'Putti' : 'Puttla';
  const bastaText = safeNumBags === 1 ? 'Basta' : 'Bastala';

  return `${safeYieldRatio} ${puttiText} ${safeNumBags} ${bastaText} ${safeRemainingKg.toFixed(2)} Kg's`;
};

/**
 * Validate entry input
 * @param {Object} input - Input to validate
 * @returns {Object} Validation result
 */
export const validateEntryInput = (input) => {
  const errors = {};
  
  if (!input.billNo || input.billNo.trim() === '') {
    errors.billNo = 'Bill number is required';
  }
  
  if (!input.tractorNo || input.tractorNo.trim() === '') {
    errors.tractorNo = 'Tractor number is required';
  }
  
  if (!input.grossWeight || isNaN(input.grossWeight) || input.grossWeight <= 0) {
    errors.grossWeight = 'Valid gross weight is required';
  }
  
  if (input.tractorWeight === undefined || isNaN(input.tractorWeight) || input.tractorWeight < 0) {
    errors.tractorWeight = 'Valid tractor weight is required';
  }
  
  if (input.rewashPercentage === undefined || isNaN(input.rewashPercentage) || input.rewashPercentage < 0) {
    errors.rewashPercentage = 'Valid rewash percentage is required';
  }
  
  if (!input.pricePer900 || isNaN(input.pricePer900) || input.pricePer900 <= 0) {
    errors.pricePer900 = 'Valid price per 900kg is required';
  }
  
  if (input.grossWeight && input.tractorWeight && input.tractorWeight >= input.grossWeight) {
    errors.tractorWeight = 'Tractor weight must be less than gross weight';
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

/**
 * Format currency display
 * @param {number} amount - Amount to format
 * @returns {string} Formatted currency
 */
export const formatCurrency = (amount) => {
  // Convert to number and check if it's a valid number
  const numAmount = Number(amount);
  if (amount === null || amount === undefined || isNaN(numAmount) || typeof numAmount !== 'number') {
    return '₹0.00';
  }
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(numAmount);
};

/**
 * Format weight display
 * @param {number} weight - Weight to format
 * @returns {string} Formatted weight
 */
export const formatWeight = (weight) => {
  // Convert to number and check if it's a valid number
  const numWeight = Number(weight);
  if (weight === null || weight === undefined || isNaN(numWeight) || typeof numWeight !== 'number') {
    return '0.00 kg';
  }
  return `${numWeight.toFixed(2)} kg`;
};

/**
 * Generate receipt number
 * @param {string} billNo - First bill number
 * @returns {string} Receipt number
 */
export const generateReceiptNumber = (billNo) => {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  return `SS-${year}${month}-${billNo}`;
};

/**
 * Generate PDF filename
 * @param {string} firstBillNo - First bill number
 * @returns {string} PDF filename
 */
export const generatePdfFilename = (firstBillNo) => {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
  return `SS_Receipt_${firstBillNo}_${timestamp}.pdf`;
};

/**
 * Calculate batch summary for multiple entries
 * @param {Array} entries - Array of entries
 * @returns {Object} Batch summary
 */
export const calculateBatchSummary = (entries) => {
  if (!entries || entries.length === 0) {
    return null;
  }

  const totals = calculateTotals(entries);
  const firstBillNo = entries[0]?.billNo || '';
  const receiptNumber = generateReceiptNumber(firstBillNo);
  
  return {
    receiptNumber,
    entryCount: entries.length,
    totalPrice: totals.totalPrice,
    totalAdjustedWeight: totals.totalAdjustedWeight,
    yieldSummary: formatYieldDisplay(
      totals.overallYieldRatio,
      totals.overallBags,
      totals.overallRemainingKg
    ),
    createdAt: new Date().toISOString()
  };
};
