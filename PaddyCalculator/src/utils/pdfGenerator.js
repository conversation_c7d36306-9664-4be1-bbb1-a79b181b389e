import * as Print from 'expo-print';
import * as Sharing from 'expo-sharing';
import {
  formatCurrency,
  formatWeight,
  formatYieldDisplay,
  generateReceiptNumber,
  calculateTotals
} from './calculations';
import { getSetting } from '../database/database';

/**
 * Generate PDF receipt for paddy calculations
 * @param {Array} entries - Array of calculated entries
 * @param {Object} options - PDF generation options
 * @param {Array} farmers - Array of farmers data
 * @param {Array} tractors - Array of tractors data
 * @returns {Promise<string>} - Path to generated PDF
 */
export const generatePaddyReceiptPDF = async (entries, options = {}, farmers = [], tractors = []) => {
  try {
    if (!entries || entries.length === 0) {
      throw new Error('No entries provided for PDF generation');
    }

    // Get default rewash percentage from settings
    const defaultRewashPercentage = await getSetting('default_rewash_percentage') || '2.0';

    const totals = calculateTotals(entries);
    const receiptNumber = generateReceiptNumber(entries[0].billNo || entries[0].bill_no || entries[0].generatedBillNo || 'DEFAULT');
    const currentDate = new Date().toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });

    const htmlContent = generateHTMLContent(entries, totals, receiptNumber, currentDate, farmers, tractors, defaultRewashPercentage);

    const { uri } = await Print.printToFileAsync({
      html: htmlContent,
      base64: false,
      ...options
    });

    return uri;
  } catch (error) {
    throw error;
  }
};

/**
 * Generate and share PDF receipt
 * @param {Array} entries - Array of calculated entries
 * @param {Object} options - PDF generation options
 * @param {Array} farmers - Array of farmers data
 * @param {Array} tractors - Array of tractors data
 */
export const generateAndSharePDF = async (entries, options = {}, farmers = [], tractors = []) => {
  try {
    const pdfUri = await generatePaddyReceiptPDF(entries, options, farmers, tractors);
    
    if (await Sharing.isAvailableAsync()) {
      await Sharing.shareAsync(pdfUri);
    } else {
      return pdfUri;
    }
  } catch (error) {
    throw error;
  }
};

/**
 * Generate parties section for PDF
 * @param {Array} entries - Array of calculated entries
 * @param {Array} farmers - Array of farmers data
 * @param {Array} tractors - Array of tractors data
 * @returns {string} HTML content for parties section
 */
const generatePartiesSection = (entries, farmers, tractors) => {
  // Get unique buyers, sellers, and tractors from entries
  const uniqueBuyers = [...new Set(entries.map(e => e.buyerId).filter(Boolean))]
    .map(id => farmers.find(f => f.id === id))
    .filter(Boolean);

  const uniqueSellers = [...new Set(entries.map(e => e.sellerId).filter(Boolean))]
    .map(id => farmers.find(f => f.id === id))
    .filter(Boolean);

  const uniqueTractors = [...new Set(entries.map(e => e.tractorId).filter(Boolean))]
    .map(id => tractors.find(t => t.id === id))
    .filter(Boolean);

  if (uniqueBuyers.length === 0 && uniqueSellers.length === 0 && uniqueTractors.length === 0) {
    return '';
  }

  return `
    <div class="parties-section">
      <h2>Transaction Details</h2>
      <div class="parties-grid">
        ${uniqueBuyers.length > 0 ? `
          <div class="party-group">
            <h3>Buyers</h3>
            ${uniqueBuyers.map(buyer => `
              <div class="party-info">
                <strong>${buyer.name}</strong>
                ${buyer.phone ? `<br>Phone: ${buyer.phone}` : ''}
                ${buyer.village ? `<br>Village: ${buyer.village}` : ''}
              </div>
            `).join('')}
          </div>
        ` : ''}

        ${uniqueSellers.length > 0 ? `
          <div class="party-group">
            <h3>Sellers</h3>
            ${uniqueSellers.map(seller => `
              <div class="party-info">
                <strong>${seller.name}</strong>
                ${seller.phone ? `<br>Phone: ${seller.phone}` : ''}
                ${seller.village ? `<br>Village: ${seller.village}` : ''}
              </div>
            `).join('')}
          </div>
        ` : ''}

        ${uniqueTractors.length > 0 ? `
          <div class="party-group">
            <h3>Tractors</h3>
            ${uniqueTractors.map(tractor => `
              <div class="party-info">
                <strong>${tractor.vehicle_number}</strong>
                ${tractor.brand ? `<br>Brand: ${tractor.brand} ${tractor.model || ''}` : ''}
                ${tractor.driver_name ? `<br>Driver: ${tractor.driver_name}` : ''}
                ${tractor.driver_phone ? `<br>Phone: ${tractor.driver_phone}` : ''}
              </div>
            `).join('')}
          </div>
        ` : ''}
      </div>
    </div>
  `;
};

/**
 * Generate HTML content for PDF
 * @param {Array} entries - Array of calculated entries
 * @param {Object} totals - Calculated totals
 * @param {string} receiptNumber - Receipt number
 * @param {string} currentDate - Current date
 * @param {Object} options - Generation options
 * @param {Array} farmers - Array of farmers data
 * @param {Array} tractors - Array of tractors data
 * @param {string} defaultRewashPercentage - Default rewash percentage from settings
 * @returns {string} HTML content
 */
const generateHTMLContent = (entries, totals, receiptNumber, currentDate, farmers = [], tractors = [], defaultRewashPercentage = '2.0') => {
  // Company Information (static)
  const companyInfo = {
    name: 'CHEVURU SOFTWARE SOLUTIONS',
    address: 'West Street, Kagithalapuru, Pincode 524405',
    phone: '+91 9502953463',
    email: '<EMAIL>',
    website: 'www.chevurusoftwaresolutions.com'
  };

  // App Information
  const appInfo = {
    name: 'Paddy Calculator',
    tagline: 'Rice Trading Platform',
    features: 'Professional Paddy Trading & Calculation System',
    developer: 'Developed by Sumanth Chevuru',
    version: '1.0.0'
  };

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Paddy Receipt - ${receiptNumber}</title>
      <style>
        ${getCSS()}
      </style>
    </head>
    <body>
      <div class="container">
        <!-- Professional Header -->
        <div class="header">
          <div class="header-top">
            <div class="company-section">
              <h1 class="company-name">${companyInfo.name}</h1>
              <div class="company-details">
                <p class="address">${companyInfo.address}</p>
                <p class="contact">Phone: ${companyInfo.phone} | Email: ${companyInfo.email}</p>
                <p class="website">Website: ${companyInfo.website}</p>
              </div>
            </div>
            <div class="app-section">
              <h2 class="app-name">${appInfo.name}</h2>
              <p class="app-tagline">${appInfo.tagline}</p>
              <p class="app-version">Version ${appInfo.version}</p>
            </div>
          </div>
          <div class="receipt-info">
            <div class="receipt-details">
              <div class="receipt-item">
                <span class="label">Receipt No:</span>
                <span class="value">${receiptNumber}</span>
              </div>
              <div class="receipt-item">
                <span class="label">Date & Time:</span>
                <span class="value">${currentDate}</span>
              </div>
              <div class="receipt-item">
                <span class="label">Default Rewash:</span>
                <span class="value">${defaultRewashPercentage}%</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Transaction Parties -->
        ${generatePartiesSection(entries, farmers, tractors)}

        <!-- Entries Table -->
        <div class="entries-section">
          <h2>Paddy Purchase Details</h2>
          <table class="entries-table">
            <thead>
              <tr>
                <th>S.No</th>
                <th>Bill No.</th>
                <th>Buyer</th>
                <th>Seller</th>
                <th>Tractor</th>
                <th>Gross (kg)</th>
                <th>Tractor (kg)</th>
                <th>Net (kg)</th>
                <th>Rewash (%)</th>
                <th>Adjusted (kg)</th>
                <th>Price/900</th>
                <th>Total Price</th>
              </tr>
            </thead>
            <tbody>
              ${entries.map((entry, index) => {
                const buyer = farmers.find(f => f.id === (entry.buyerId || entry.buyer_id));
                const seller = farmers.find(f => f.id === (entry.sellerId || entry.seller_id));
                const tractor = tractors.find(t => t.id === (entry.tractorId || entry.tractor_id));

                return `
                <tr>
                  <td>${index + 1}</td>
                  <td>${entry.billNo || entry.bill_no || entry.generatedBillNo || '-'}</td>
                  <td>${buyer?.name || '-'}</td>
                  <td>${seller?.name || '-'}</td>
                  <td>${tractor?.vehicle_number || tractor?.number || entry.tractorNo || entry.tractor_no || '-'}</td>
                  <td>${(entry.grossWeight || entry.gross_weight || 0).toFixed(2)}</td>
                  <td>${(entry.tractorWeight || entry.tractor_weight || 0).toFixed(2)}</td>
                  <td>${(entry.netWeight || entry.net_weight || 0).toFixed(2)}</td>
                  <td>${(entry.rewashPercentage || entry.rewash_percentage || 0).toFixed(1)}%</td>
                  <td>${(entry.adjustedWeight || entry.adjusted_weight || 0).toFixed(2)}</td>
                  <td>${formatCurrency(entry.pricePer900 || entry.price_per_900 || 0)}</td>
                  <td class="price">${formatCurrency(entry.calculatedPrice || entry.totalPrice || entry.total_price || 0)}</td>
                </tr>
                `;
              }).join('')}
            </tbody>
            <tfoot>
              <tr class="total-row">
                <td colspan="5"><strong>TOTAL</strong></td>
                <td><strong>${totals.totalGrossWeight.toFixed(2)}</strong></td>
                <td><strong>${totals.totalTractorWeight.toFixed(2)}</strong></td>
                <td><strong>${totals.totalNetWeight.toFixed(2)}</strong></td>
                <td>-</td>
                <td><strong>${totals.totalAdjustedWeight.toFixed(2)}</strong></td>
                <td>-</td>
                <td class="total-price"><strong>${formatCurrency(totals.totalPrice)}</strong></td>
              </tr>
            </tfoot>
          </table>
        </div>

        <!-- Yield Summary -->
        <div class="yield-section">
          <h3>Yield Summary</h3>
          ${entries.map((entry, index) => `
            <div class="yield-entry">
              <strong>Entry ${index + 1}:</strong>
              ${formatYieldDisplay(entry.yieldRatio, entry.numBags, entry.remainingKg)}
            </div>
          `).join('')}

          <div class="total-yield">
            <strong>Overall Total:</strong>
            ${formatYieldDisplay(totals.overallYieldRatio, totals.overallBags, totals.overallRemainingKg)}
          </div>
        </div>

        <!-- Summary -->
        <div class="summary-section">
          <div class="summary-grid">
            <div class="summary-item">
              <span class="label">Total Entries:</span>
              <span class="value">${totals.entryCount}</span>
            </div>
            <div class="summary-item">
              <span class="label">Total Gross Weight:</span>
              <span class="value">${formatWeight(totals.totalGrossWeight)}</span>
            </div>
            <div class="summary-item">
              <span class="label">Total Adjusted Weight:</span>
              <span class="value">${formatWeight(totals.totalAdjustedWeight)}</span>
            </div>
            <div class="summary-item highlight">
              <span class="label">TOTAL AMOUNT:</span>
              <span class="value">${formatCurrency(totals.totalPrice)}</span>
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div class="footer">
          <p>This is a computer generated receipt • Not valid without signature • Errors and omissions excepted</p>
          <p>Generated on: ${new Date().toLocaleString('en-IN')} • Document ID: ${generateDocumentId()}</p>
        </div>
      </div>
    </body>
    </html>
  `;
};

/**
 * Get CSS styles for PDF
 * @returns {string} CSS styles
 */
const getCSS = () => `
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    font-family: 'Arial', sans-serif;
    font-size: 12px;
    line-height: 1.4;
    color: #333;
    background: #fff;
  }

  .container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
  }

  .header {
    margin-bottom: 30px;
    border-bottom: 3px solid #1B5E20;
    padding-bottom: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e8f5e8 100%);
    border-radius: 8px 8px 0 0;
    padding: 20px;
  }

  .header-top {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
  }

  .company-section {
    flex: 1;
    text-align: left;
  }

  .company-name {
    font-size: 22px;
    font-weight: bold;
    color: #1B5E20;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 1px;
  }

  .company-details {
    color: #2E7D32;
    font-size: 11px;
    line-height: 1.4;
  }

  .company-details .address {
    font-weight: bold;
    margin-bottom: 4px;
  }

  .company-details .contact,
  .company-details .website {
    margin: 2px 0;
    color: #555;
  }

  .app-section {
    text-align: right;
    flex: 1;
  }

  .app-name {
    font-size: 20px;
    font-weight: bold;
    color: #1B5E20;
    margin-bottom: 5px;
  }

  .app-tagline {
    font-size: 12px;
    color: #2E7D32;
    font-style: italic;
    margin-bottom: 5px;
  }

  .app-version {
    font-size: 10px;
    color: #333;
    background: #FFD600;
    padding: 2px 8px;
    border-radius: 12px;
    display: inline-block;
    font-weight: bold;
  }

  .receipt-info {
    background: #1B5E20;
    color: white;
    padding: 15px;
    border-radius: 8px;
    margin-top: 15px;
  }

  .receipt-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .receipt-item {
    text-align: center;
    flex: 1;
  }

  .receipt-item .label {
    display: block;
    font-size: 10px;
    opacity: 0.8;
    margin-bottom: 2px;
  }

  .receipt-item .value {
    display: block;
    font-size: 12px;
    font-weight: bold;
  }

  .parties-section {
    margin-bottom: 25px;
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #1B5E20;
  }

  .parties-section h2 {
    color: #1B5E20;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: bold;
  }

  .parties-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
  }

  .party-group {
    flex: 1;
    min-width: 200px;
  }

  .party-group h3 {
    color: #1B5E20;
    margin-bottom: 10px;
    font-size: 14px;
    border-bottom: 2px solid #FFD600;
    padding-bottom: 5px;
    font-weight: bold;
  }

  .party-info {
    background: white;
    padding: 12px;
    margin-bottom: 8px;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
    font-size: 11px;
    line-height: 1.4;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  }

  .party-info strong {
    color: #1B5E20;
    font-size: 12px;
    display: block;
    margin-bottom: 4px;
  }

  .entries-section {
    margin-bottom: 30px;
  }

  .entries-section h2 {
    color: #1B5E20;
    margin-bottom: 15px;
    font-size: 18px;
    font-weight: bold;
    border-bottom: 2px solid #FFD600;
    padding-bottom: 8px;
  }

  .entries-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    font-size: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-radius: 8px;
    overflow: hidden;
  }

  .entries-table th,
  .entries-table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: center;
  }

  .entries-table th {
    background: linear-gradient(135deg, #1B5E20 0%, #2E7D32 100%);
    color: white;
    font-weight: bold;
    font-size: 9px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .entries-table tbody tr:nth-child(even) {
    background-color: #f9f9f9;
  }

  .entries-table tbody tr:hover {
    background-color: #f0f8f0;
  }

  .entries-table .price {
    font-weight: bold;
    color: #1B5E20;
  }

  .total-row {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c8 100%) !important;
    font-weight: bold;
    border-top: 2px solid #1B5E20;
  }

  .total-price {
    color: #1B5E20;
    font-size: 12px;
    font-weight: bold;
  }

  .yield-section {
    margin-bottom: 30px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e8f5e8 100%);
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #FFD600;
  }

  .yield-section h3 {
    color: #1B5E20;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: bold;
  }

  .yield-entry {
    margin: 8px 0;
    padding: 8px;
    background: white;
    border-radius: 5px;
    border-left: 3px solid #2E7D32;
    font-size: 11px;
  }

  .total-yield {
    margin-top: 15px;
    padding: 12px;
    background: linear-gradient(135deg, #1B5E20 0%, #2E7D32 100%);
    color: white;
    border-radius: 6px;
    font-size: 14px;
    font-weight: bold;
    text-align: center;
  }

  .summary-section {
    margin-bottom: 30px;
  }

  .summary-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
  }

  .summary-item {
    display: flex;
    justify-content: space-between;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #2E7D32;
  }

  .summary-item.highlight {
    background: linear-gradient(135deg, #1B5E20 0%, #2E7D32 100%);
    color: white;
    font-weight: bold;
    font-size: 14px;
    border-left: 3px solid #FFD600;
  }

  .summary-item .label {
    font-weight: bold;
  }

  .footer {
    text-align: center;
    font-size: 10px;
    color: #666;
    border-top: 2px solid #e0e0e0;
    padding-top: 15px;
    margin-top: 30px;
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
  }

  .footer p {
    margin: 5px 0;
  }

  @media print {
    .container {
      padding: 10px;
    }

    .entries-table {
      font-size: 9px;
    }

    .header {
      background: #f8f9fa !important;
    }
  }
`;

/**
 * Generate unique document ID
 * @returns {string} Document ID
 */
const generateDocumentId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
};

export default {
  generatePaddyReceiptPDF,
  generateAndSharePDF
};
