/**
 * Theme constants for Paddy Calculator
 * Modern and flexible design system
 */

export const Colors = {
  // Primary colors - Dark Green
  primary: '#1B5E20',      // Dark Green primary
  primaryDark: '#0D2818',  // Darker green
  primaryLight: '#2E7D32', // Medium green
  primaryLowOpacity: 'rgba(27, 94, 32, 0.05)', // Green with very low opacity
  primaryMediumOpacity: 'rgba(27, 94, 32, 0.1)', // Green with medium opacity

  // Secondary colors - Yellow
  secondary: '#FFD600',    // Bright Yellow
  secondaryDark: '#FFC107', // Amber Yellow
  secondaryLight: '#FFEB3B', // Light Yellow
  secondaryLowOpacity: 'rgba(255, 214, 0, 0.08)', // Yellow with very low opacity
  secondaryMediumOpacity: 'rgba(255, 214, 0, 0.15)', // Yellow with medium opacity

  // Background colors
  background: '#F8F9FA',   // Very light gray background
  surface: '#FFFFFF',      // White surface
  surfaceDark: '#1B5E20',  // Dark green surface

  // Text colors
  text: '#1B5E20',         // Dark green text
  textSecondary: '#2E7D32', // Medium green text
  textLight: '#FFFFFF',    // White text

  // Status colors - Using Dark Green and Yellow theme
  success: '#1B5E20',      // Dark Green for success
  warning: '#FFD600',      // Yellow for warning
  error: '#D32F2F',        // Keep red for errors (safety)
  info: '#1B5E20',         // Dark Green for info

  // Border colors
  border: '#E8F5E8',       // Very light green
  borderDark: '#2E7D32',   // Medium green

  // Accent colors
  accent: '#FFD600',       // Yellow accent

  // Transparent overlays
  overlay: 'rgba(27, 94, 32, 0.5)',      // Dark green overlay
  overlayLight: 'rgba(27, 94, 32, 0.2)', // Light dark green overlay
};

export const Spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

export const Typography = {
  // Font sizes
  fontSize: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
    xxxl: 32,
  },
  
  // Font weights
  fontWeight: {
    light: '300',
    regular: '400',
    medium: '500',
    semiBold: '600',
    bold: '700',
  },
  
  // Line heights
  lineHeight: {
    tight: 1.2,
    normal: 1.4,
    relaxed: 1.6,
  },
};

export const BorderRadius = {
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  round: 50,
};

export const Shadows = {
  sm: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,
    elevation: 1,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.23,
    shadowRadius: 2.62,
    elevation: 4,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.30,
    shadowRadius: 4.65,
    elevation: 8,
  },
};

export const Layout = {
  // Container widths
  containerPadding: Spacing.md,
  maxWidth: 1200,
  
  // Header heights
  headerHeight: 60,
  tabBarHeight: 60,
  
  // Input heights
  inputHeight: 48,
  buttonHeight: 48,
};

// Paper theme configuration for react-native-paper
export const PaperTheme = {
  colors: {
    primary: Colors.primary,
    accent: Colors.secondary,
    background: Colors.background,
    surface: Colors.surface,
    text: Colors.text,
    disabled: Colors.textSecondary,
    placeholder: Colors.textSecondary,
    backdrop: Colors.overlay,
    onSurface: Colors.text,
    notification: Colors.error,
  },
  fonts: {
    regular: {
      fontFamily: 'System',
      fontWeight: Typography.fontWeight.regular,
    },
    medium: {
      fontFamily: 'System',
      fontWeight: Typography.fontWeight.medium,
    },
    light: {
      fontFamily: 'System',
      fontWeight: Typography.fontWeight.light,
    },
    thin: {
      fontFamily: 'System',
      fontWeight: Typography.fontWeight.light,
    },
    // Add missing font variants
    titleLarge: {
      fontFamily: 'System',
      fontWeight: Typography.fontWeight.bold,
      fontSize: Typography.fontSize.xxl,
    },
    titleMedium: {
      fontFamily: 'System',
      fontWeight: Typography.fontWeight.medium,
      fontSize: Typography.fontSize.lg,
    },
    titleSmall: {
      fontFamily: 'System',
      fontWeight: Typography.fontWeight.medium,
      fontSize: Typography.fontSize.md,
    },
    bodyLarge: {
      fontFamily: 'System',
      fontWeight: Typography.fontWeight.regular,
      fontSize: Typography.fontSize.md,
    },
    bodyMedium: {
      fontFamily: 'System',
      fontWeight: Typography.fontWeight.regular,
      fontSize: Typography.fontSize.sm,
    },
    bodySmall: {
      fontFamily: 'System',
      fontWeight: Typography.fontWeight.regular,
      fontSize: Typography.fontSize.xs,
    },
    labelLarge: {
      fontFamily: 'System',
      fontWeight: Typography.fontWeight.medium,
      fontSize: Typography.fontSize.sm,
    },
    labelMedium: {
      fontFamily: 'System',
      fontWeight: Typography.fontWeight.medium,
      fontSize: Typography.fontSize.xs,
    },
    labelSmall: {
      fontFamily: 'System',
      fontWeight: Typography.fontWeight.medium,
      fontSize: Typography.fontSize.xs,
    },
  },
  roundness: BorderRadius.md,
};

// Icon sizes
export const IconSizes = {
  xs: 16,
  sm: 20,
  md: 24,
  lg: 32,
  xl: 40,
};

// Animation durations
export const AnimationDurations = {
  fast: 150,
  normal: 300,
  slow: 500,
};

// Screen breakpoints
export const Breakpoints = {
  mobile: 480,
  tablet: 768,
  desktop: 1024,
};

export default {
  Colors,
  Spacing,
  Typography,
  BorderRadius,
  Shadows,
  Layout,
  PaperTheme,
  IconSizes,
  AnimationDurations,
  Breakpoints,
};
