# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in /usr/local/Cellar/android-sdk/24.3.3/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# react-native-reanimated
-keep class com.swmansion.reanimated.** { *; }
-keep class com.facebook.react.turbomodule.** { *; }

# Add any project specific keep options here:

# Keep Expo modules
-keep class expo.modules.** { *; }
-keep class com.facebook.hermes.** { *; }

# Keep SQLite
-keep class org.sqlite.** { *; }
-keep class org.sqlite.database.** { *; }

# Keep PDF and sharing functionality
-keep class expo.modules.print.** { *; }
-keep class expo.modules.sharing.** { *; }

# Keep React Native Paper
-keep class com.callstack.reanimated.** { *; }

# Vivo specific optimizations
-keep class com.vivo.** { *; }
-keep class android.support.** { *; }
-keep class androidx.** { *; }

# Android 15 compatibility
-keep class android.app.** { *; }
-keep class android.content.** { *; }

# Prevent obfuscation of main application
-keep class com.chevurusoftware.paddycalculator.** { *; }
